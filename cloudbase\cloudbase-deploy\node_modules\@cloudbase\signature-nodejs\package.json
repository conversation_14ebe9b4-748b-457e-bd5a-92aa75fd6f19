{"name": "@cloudbase/signature-nodejs", "version": "1.0.0-beta.0", "description": "cloudbase api signature for node.js", "main": "lib/index.js", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "repository": {"type": "git", "url": "*******************:QBase/cloudbase-signature-nodejs.git"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "dependencies": {"@types/clone": "^0.1.30", "clone": "^2.1.2", "is-stream": "^2.0.0", "url": "^0.11.0"}, "devDependencies": {"@types/jest": "^24.0.23", "@types/node": "10.12.10", "@typescript-eslint/eslint-plugin": "^2.10.0", "@typescript-eslint/parser": "^2.10.0", "eslint": "^6.7.2", "eslint-plugin-typescript": "^0.14.0", "husky": "^3.1.0", "jest": "^24.9.0", "lint-staged": "^9.5.0", "ts-jest": "^24.2.0", "typescript": "3.5.3", "typescript-eslint-parser": "^22.0.0"}}