/*! lowdb v1.0.0 */
var LocalStorage=function(e){function t(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return e[n].call(o.exports,o,o.exports,t),o.l=!0,o.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,n){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:n})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="",t(t.s=3)}([,,,function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}var u=function(){function e(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),a=r(4),s=function(e){function t(){return n(this,t),o(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return i(t,e),u(t,[{key:"read",value:function(){var e=localStorage.getItem(this.source);return e?this.deserialize(e):(localStorage.setItem(this.source,this.serialize(this.defaultValue)),this.defaultValue)}},{key:"write",value:function(e){localStorage.setItem(this.source,this.serialize(e))}}]),t}(a);e.exports=s},function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var o=r(5),i=function e(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=r.defaultValue,u=void 0===i?{}:i,a=r.serialize,s=void 0===a?o:a,c=r.deserialize,l=void 0===c?JSON.parse:c;n(this,e),this.source=t,this.defaultValue=u,this.serialize=s,this.deserialize=l};e.exports=i},function(e,t,r){"use strict";e.exports=function(e){return JSON.stringify(e,null,2)}}]);