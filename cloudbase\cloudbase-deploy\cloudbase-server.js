const jsonServer = require('json-server');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const crypto = require('crypto');
const multer = require('multer');
const fs = require('fs');
const path = require('path');

// 确保数据目录存在
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 确保db.json文件存在
const dbPath = path.join(dataDir, 'db.json');
if (!fs.existsSync(dbPath)) {
  // 复制原始db.json文件
  fs.copyFileSync(path.join(__dirname, 'db.json'), dbPath);
}

const server = jsonServer.create();
const router = jsonServer.router(dbPath);
const middlewares = jsonServer.defaults();

// 配置multer用于文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 5 * 1024 * 1024 } // 5MB限制
});

const SECRET_KEY = 'your-secret-key';
const db = router.db; // 获取数据库实例

// 密码哈希函数（与Flutter端保持一致）
function hashPassword(password) {
  return crypto.createHash('sha256').update(password).digest('hex');
}

// 启用CORS
server.use(cors());
server.use(middlewares);
server.use(jsonServer.bodyParser);

// 添加请求日志中间件
server.use((req, res, next) => {
  console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
  next();
});

// 生成JWT Token
function generateToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '24h' });
}

// 生成刷新Token
function generateRefreshToken(userId) {
  return jwt.sign({ userId }, SECRET_KEY, { expiresIn: '30d' });
}

// 验证Token中间件
function verifyToken(req, res, next) {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.status(401).json({ success: false, message: '未提供认证令牌' });
  }

  const token = authHeader.split(' ')[1];
  try {
    const decoded = jwt.verify(token, SECRET_KEY);
    req.userId = decoded.userId;
    next();
  } catch (error) {
    return res.status(401).json({ success: false, message: '无效的认证令牌' });
  }
}

// 发送验证码
server.post('/api/auth/send-code', (req, res) => {
  const { phoneNumber } = req.body;
  
  // 模拟发送验证码
  console.log(`发送验证码到 ${phoneNumber}: 123456`);
  
  res.json({
    success: true,
    message: '验证码发送成功'
  });
});

// 验证验证码
server.post('/api/auth/verify-code', (req, res) => {
  const { phoneNumber, code } = req.body;
  
  // 模拟验证码验证（固定为123456）
  if (code === '123456') {
    res.json({
      success: true,
      message: '验证码验证成功'
    });
  } else {
    res.status(400).json({
      success: false,
      message: '验证码错误'
    });
  }
});

// 用户注册
server.post('/api/auth/register', (req, res) => {
  const { username, password, phoneNumber, verificationCode, memberCode } = req.body;
  
  // 检查用户是否已存在
  const existingUser = db.get('users').find({ username }).value() || 
                      db.get('users').find({ phoneNumber }).value();
  
  if (existingUser) {
    return res.status(400).json({
      success: false,
      message: '用户名或手机号已存在'
    });
  }
  
  // 验证会员码
  let memberInfo = null;
  if (memberCode) {
    const code = db.get('memberCodes').find({ code: memberCode, isUsed: false }).value();
    if (!code) {
      return res.status(400).json({
        success: false,
        message: '会员码无效'
      });
    }
    memberInfo = code;
  }
  
  // 创建新用户
  const userId = uuidv4();
  const newUser = {
    id: userId,
    username,
    phoneNumber,
    email: null,
    avatar: null,
    passwordHash: password, // 存储密码哈希（Flutter端已经哈希过了）
    isMember: !!memberInfo,
    memberExpireTime: memberInfo ? null : null,
    membershipType: memberInfo ? 'permanent' : 'none',
    isPermanentMember: !!memberInfo,
    memberCode: memberCode || null,
    isDataSyncEnabled: true,
    settings: {
      enableBiometric: false,
      autoSync: true,
      enableNotification: true,
      theme: 'system',
      language: 'zh-CN'
    },
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  db.get('users').push(newUser).write();
  
  // 如果使用了会员码，标记为已使用
  if (memberInfo) {
    db.get('memberCodes')
      .find({ code: memberCode })
      .assign({
        isUsed: true,
        usedBy: userId,
        usedAt: new Date().toISOString()
      })
      .write();
  }
  
  // 生成Token
  const token = generateToken(userId);
  const refreshToken = generateRefreshToken(userId);
  
  res.json({
    success: true,
    data: {
      token,
      refreshToken,
      user: newUser,
      expiresIn: 86400
    }
  });
});

// 用户登录
server.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  // 查找用户
  const user = db.get('users').find({ username }).value();

  if (!user) {
    return res.status(400).json({
      success: false,
      message: '用户名或密码错误'
    });
  }

  // 验证密码（检查哈希值）
  console.log(`用户 ${username} 尝试登录，密码哈希: ${password}`);

  // 从数据库获取用户的密码哈希
  const storedPasswordHash = user.passwordHash;
  console.log(`数据库中的密码哈希: ${storedPasswordHash}`);

  // 如果数据库中没有密码哈希，使用默认密码进行兼容
  if (!storedPasswordHash) {
    const validPasswordHashes = [
      hashPassword('test123'),
      hashPassword('password'),
      hashPassword('123456')
    ];

    if (!validPasswordHashes.includes(password)) {
      console.log(`密码验证失败（使用默认密码）`);
      return res.status(400).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
  } else {
    // 使用数据库中存储的密码哈希验证
    if (password !== storedPasswordHash) {
      console.log(`密码验证失败（数据库密码不匹配）`);
      return res.status(400).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
  }

  console.log(`用户 ${username} 登录成功`);

  // 生成Token
  const token = generateToken(user.id);
  const refreshToken = generateRefreshToken(user.id);

  res.json({
    success: true,
    data: {
      token,
      refreshToken,
      user,
      expiresIn: 86400
    }
  });
});

// 获取会员套餐
server.get('/packages', (req, res) => {
  const packages = db.get('packages').filter({ isActive: true }).value();
  res.json({
    success: true,
    data: packages
  });
});

// 验证会员码
server.post('/api/member-code/validate', (req, res) => {
  const { code } = req.body;

  const memberCode = db.get('memberCodes').find({ code, isUsed: false }).value();

  if (memberCode && (!memberCode.expireAt || new Date(memberCode.expireAt) > new Date())) {
    res.json({
      success: true,
      message: '会员码有效'
    });
  } else {
    res.status(400).json({
      success: false,
      message: '会员码无效或已过期'
    });
  }
});

// 生成会员码工具函数
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 创建单个会员码
server.post('/api/admin/member-code/create', (req, res) => {
  const { packageId, expireAt, batchId, customCode } = req.body;

  // 验证套餐是否存在
  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }

  // 生成或使用自定义会员码
  const code = customCode || generateMemberCode();

  // 检查会员码是否已存在
  const existingCode = db.get('memberCodes').find({ code }).value();
  if (existingCode) {
    return res.status(400).json({
      success: false,
      message: '会员码已存在'
    });
  }

  const newMemberCode = {
    code,
    packageId,
    isUsed: false,
    usedBy: null,
    usedAt: null,
    expireAt: expireAt || null,
    batchId: batchId || `batch_${Date.now()}`,
    createdAt: new Date().toISOString()
  };

  db.get('memberCodes').push(newMemberCode).write();

  res.json({
    success: true,
    data: newMemberCode,
    message: '会员码创建成功'
  });
});

// 批量创建会员码
server.post('/admin/member-code/batch-create', (req, res) => {
  const { packageId, count, expireAt, batchId, prefix } = req.body;

  // 验证套餐是否存在
  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }

  const createdCodes = [];
  const currentBatchId = batchId || `batch_${Date.now()}`;

  for (let i = 0; i < count; i++) {
    let code;
    let attempts = 0;

    // 生成唯一的会员码
    do {
      code = generateMemberCode(prefix || 'VIP');
      attempts++;
      if (attempts > 100) {
        return res.status(500).json({
          success: false,
          message: '生成唯一会员码失败，请重试'
        });
      }
    } while (db.get('memberCodes').find({ code }).value());

    const newMemberCode = {
      code,
      packageId,
      isUsed: false,
      usedBy: null,
      usedAt: null,
      expireAt: expireAt || null,
      batchId: currentBatchId,
      createdAt: new Date().toISOString()
    };

    db.get('memberCodes').push(newMemberCode).write();
    createdCodes.push(newMemberCode);
  }

  res.json({
    success: true,
    data: {
      batchId: currentBatchId,
      count: createdCodes.length,
      codes: createdCodes
    },
    message: `成功创建 ${createdCodes.length} 个会员码`
  });
});

// 获取会员码列表
server.get('/admin/member-code/list', (req, res) => {
  const { page = 1, limit = 20, isUsed, packageId, batchId } = req.query;

  let memberCodes = db.get('memberCodes').value();

  // 过滤条件
  if (isUsed !== undefined) {
    memberCodes = memberCodes.filter(code => code.isUsed === (isUsed === 'true'));
  }

  if (packageId) {
    memberCodes = memberCodes.filter(code => code.packageId === packageId);
  }

  if (batchId) {
    memberCodes = memberCodes.filter(code => code.batchId === batchId);
  }

  // 分页
  const total = memberCodes.length;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + parseInt(limit);
  const paginatedCodes = memberCodes.slice(startIndex, endIndex);

  res.json({
    success: true,
    data: {
      codes: paginatedCodes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  });
});

// 删除会员码
server.delete('/admin/member-code/:code', (req, res) => {
  const { code } = req.params;

  const memberCode = db.get('memberCodes').find({ code }).value();
  if (!memberCode) {
    return res.status(404).json({
      success: false,
      message: '会员码不存在'
    });
  }

  if (memberCode.isUsed) {
    return res.status(400).json({
      success: false,
      message: '已使用的会员码不能删除'
    });
  }

  db.get('memberCodes').remove({ code }).write();

  res.json({
    success: true,
    message: '会员码删除成功'
  });
});

// 获取会员码统计信息
server.get('/admin/member-code/stats', (req, res) => {
  const memberCodes = db.get('memberCodes').value();

  const stats = {
    total: memberCodes.length,
    used: memberCodes.filter(code => code.isUsed).length,
    unused: memberCodes.filter(code => !code.isUsed).length,
    expired: memberCodes.filter(code =>
      code.expireAt && new Date(code.expireAt) < new Date()
    ).length,
    byPackage: {},
    byBatch: {}
  };

  // 按套餐统计
  memberCodes.forEach(code => {
    if (!stats.byPackage[code.packageId]) {
      stats.byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
    }
    stats.byPackage[code.packageId].total++;
    if (code.isUsed) {
      stats.byPackage[code.packageId].used++;
    } else {
      stats.byPackage[code.packageId].unused++;
    }
  });

  // 按批次统计
  memberCodes.forEach(code => {
    if (!stats.byBatch[code.batchId]) {
      stats.byBatch[code.batchId] = { total: 0, used: 0, unused: 0 };
    }
    stats.byBatch[code.batchId].total++;
    if (code.isUsed) {
      stats.byBatch[code.batchId].used++;
    } else {
      stats.byBatch[code.batchId].unused++;
    }
  });

  res.json({
    success: true,
    data: stats
  });
});

// 创建订单
server.post('/orders/create', verifyToken, (req, res) => {
  const { packageId } = req.body;
  const userId = req.userId;

  const package = db.get('packages').find({ id: packageId }).value();
  if (!package) {
    return res.status(400).json({
      success: false,
      message: '套餐不存在'
    });
  }

  const orderId = uuidv4();
  const newOrder = {
    id: orderId,
    userId,
    packageId,
    packageName: package.name,
    amount: package.price,
    status: 'pending',
    paymentMethod: null,
    transactionId: null,
    memberCode: null,
    expireAt: new Date(Date.now() + 30 * 60 * 1000).toISOString(), // 30分钟后过期
    paidAt: null,
    metadata: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  db.get('orders').push(newOrder).write();

  res.json({
    success: true,
    data: newOrder
  });
});

// 会员码支付
server.post('/payment/member-code', verifyToken, (req, res) => {
  const { orderId, memberCode } = req.body;
  const userId = req.userId;

  const order = db.get('orders').find({ id: orderId, userId }).value();
  if (!order) {
    return res.status(400).json({
      success: false,
      message: '订单不存在'
    });
  }

  const code = db.get('memberCodes').find({ code: memberCode, isUsed: false }).value();
  if (!code) {
    return res.status(400).json({
      success: false,
      message: '会员码无效'
    });
  }

  // 更新订单状态
  db.get('orders')
    .find({ id: orderId })
    .assign({
      status: 'paid',
      paymentMethod: 'member_code',
      memberCode,
      paidAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    .write();

  // 标记会员码为已使用
  db.get('memberCodes')
    .find({ code: memberCode })
    .assign({
      isUsed: true,
      usedBy: userId,
      usedAt: new Date().toISOString()
    })
    .write();

  // 更新用户会员状态
  const package = db.get('packages').find({ id: order.packageId }).value();
  const membershipData = {
    isMember: true,
    isPermanent: package.durationDays === -1,
    membershipType: package.durationDays === -1 ? 'permanent' : 'monthly',
    expireTime: package.durationDays === -1 ? null : new Date(Date.now() + package.durationDays * 24 * 60 * 60 * 1000).toISOString()
  };

  db.get('users')
    .find({ id: userId })
    .assign({
      isMember: true,
      isPermanentMember: membershipData.isPermanent,
      membershipType: membershipData.membershipType,
      memberExpireTime: membershipData.expireTime,
      updatedAt: new Date().toISOString()
    })
    .write();

  res.json({
    success: true,
    data: membershipData
  });
});

// 数据同步上传
server.post('/api/sync/upload', verifyToken, (req, res) => {
  const { data } = req.body;
  const userId = req.userId;

  // 模拟保存同步数据
  console.log(`用户 ${userId} 上传同步数据:`, Object.keys(data));

  res.json({
    success: true,
    message: '数据同步成功'
  });
});

// 数据同步下载
server.get('/api/sync/download', verifyToken, (req, res) => {
  const userId = req.userId;

  // 模拟返回同步数据
  const syncData = {
    novels: [],
    characterCards: [],
    characterTypes: [],
    knowledgeDocuments: [],
    stylePackages: [],
    userSettings: {
      enableBiometric: false,
      autoSync: true,
      enableNotification: true,
      theme: 'system',
      language: 'zh-CN'
    }
  };

  res.json({
    success: true,
    data: syncData
  });
});

// CloudBase直传上传API
server.post('/api/sync/upload-direct', verifyToken, (req, res) => {
  const { data, timestamp, fileId, fileName, dataStats } = req.body;
  const userId = req.userId;

  // 验证用户token
  if (!userId) {
    return res.status(500).json({
      success: false,
      message: '数据上传失败: validateUserToken is not defined'
    });
  }

  // 模拟保存直传数据
  console.log(`用户 ${userId} 直传数据:`, {
    fileName,
    fileId,
    timestamp,
    dataSize: JSON.stringify(data).length,
    stats: dataStats
  });

  res.json({
    success: true,
    message: '数据直传成功',
    fileId,
    fileName,
    timestamp
  });
});

// CloudBase直传下载API
server.get('/api/sync/download-direct', verifyToken, (req, res) => {
  const userId = req.userId;

  // 模拟API端点不存在的错误（与实际日志一致）
  res.status(404).json({
    success: false,
    message: 'API endpoint not found',
    path: '/sync/download-direct',
    method: 'GET'
  });
});

// 上传头像 (UserController调用的路径)
server.post('/user/avatar', verifyToken, upload.single('avatar'), (req, res) => {
  const userId = req.userId;
  const file = req.file;

  // 模拟头像上传 (支持FormData)
  console.log(`用户 ${userId} 上传头像，文件大小: ${file ? file.size : 0} 字节`);

  if (!file) {
    return res.status(400).json({
      success: false,
      message: '未找到上传的文件'
    });
  }

  // 生成模拟头像URL - 提供多种选择
  const user = db.get('users').find({ id: userId }).value();
  const username = user ? user.username : 'User';
  const userInitials = username.slice(0, 2).toUpperCase();

  // 随机选择头像样式
  const avatarStyles = [
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=4CAF50&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=2196F3&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=FF9800&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=9C27B0&color=fff&bold=true`,
    `https://ui-avatars.com/api/?name=${userInitials}&size=150&background=F44336&color=fff&bold=true`,
    `https://robohash.org/${userId}?size=150x150&set=set4`,
    `https://avatars.dicebear.com/api/avataaars/${userId}.svg?width=150&height=150`,
    `https://avatars.dicebear.com/api/bottts/${userId}.svg?width=150&height=150`
  ];

  const randomIndex = Math.floor(Math.random() * avatarStyles.length);
  const avatarUrl = avatarStyles[randomIndex];

  console.log(`为用户 ${username}(${userId}) 生成头像: ${avatarUrl}`);

  // 更新用户头像
  db.get('users')
    .find({ id: userId })
    .assign({
      avatar: avatarUrl,
      updatedAt: new Date().toISOString()
    })
    .write();

  // 获取更新后的用户信息
  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    message: '头像上传成功',
    data: {
      avatarUrl: avatarUrl,
      user: updatedUser
    }
  });
});

// 更新用户密码
server.put('/user/password', verifyToken, (req, res) => {
  const userId = req.userId;
  const { oldPassword, newPassword } = req.body;

  console.log(`用户 ${userId} 尝试更新密码，旧密码: ${oldPassword}`);

  if (!oldPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: '请提供当前密码和新密码'
    });
  }

  // 获取用户信息
  const user = db.get('users').find({ id: userId }).value();
  if (!user) {
    return res.status(404).json({
      success: false,
      message: '用户不存在'
    });
  }

  // 验证当前密码（检查哈希值）
  console.log(`接收到的旧密码哈希: ${oldPassword}`);

  // 从数据库获取用户当前的密码哈希
  const storedPasswordHash = user.passwordHash;
  console.log(`数据库中的密码哈希: ${storedPasswordHash}`);

  // 验证当前密码
  let isCurrentPasswordValid = false;

  if (storedPasswordHash) {
    // 使用数据库中存储的密码哈希验证
    isCurrentPasswordValid = (oldPassword === storedPasswordHash);
  } else {
    // 如果数据库中没有密码哈希，使用默认密码进行兼容
    const validPasswordHashes = [
      hashPassword('test123'),
      hashPassword('password'),
      hashPassword('123456')
    ];
    isCurrentPasswordValid = validPasswordHashes.includes(oldPassword);
  }

  if (!isCurrentPasswordValid) {
    console.log(`用户 ${userId} 旧密码验证失败`);
    return res.status(400).json({
      success: false,
      message: '当前密码错误'
    });
  }

  console.log(`用户 ${userId} 密码验证成功，更新密码`);
  console.log(`新密码哈希: ${newPassword}`);

  // 更新密码哈希到数据库
  db.get('users')
    .find({ id: userId })
    .assign({
      passwordHash: newPassword, // 存储新密码的哈希值
      lastPasswordChange: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })
    .write();

  console.log(`用户 ${userId} 密码已成功更新到数据库`);

  res.json({
    success: true,
    message: '密码更新成功'
  });
});

// 更新用户设置
server.put('/user/settings', verifyToken, (req, res) => {
  const userId = req.userId;
  const settings = req.body;

  console.log(`用户 ${userId} 更新设置:`, settings);

  // 更新用户设置
  const user = db.get('users').find({ id: userId }).value();
  if (!user) {
    return res.status(404).json({
      success: false,
      message: '用户不存在'
    });
  }

  db.get('users')
    .find({ id: userId })
    .assign({
      settings: { ...user.settings, ...settings },
      updatedAt: new Date().toISOString()
    })
    .write();

  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    data: updatedUser
  });
});

// 更新用户信息
server.put('/user/profile', verifyToken, (req, res) => {
  const userId = req.userId;
  const { username, email } = req.body;

  // 检查用户名是否已被其他用户使用
  if (username) {
    const existingUser = db.get('users').find({ username, id: { $ne: userId } }).value();
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '用户名已被使用'
      });
    }
  }

  // 更新用户信息
  const updateData = {
    updatedAt: new Date().toISOString()
  };

  if (username) updateData.username = username;
  if (email) updateData.email = email;

  db.get('users')
    .find({ id: userId })
    .assign(updateData)
    .write();

  const updatedUser = db.get('users').find({ id: userId }).value();

  res.json({
    success: true,
    data: updatedUser
  });
});

// 健康检查API
server.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'CloudBase Novel App API',
    version: '1.0.0'
  });
});

// 使用默认路由，添加/api前缀
server.use('/api', router);

// 管理员认证中间件
const verifyAdmin = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '未提供认证令牌'
    });
  }

  // 简单的管理员token验证（实际项目中应该使用JWT）
  if (token.startsWith('mock_admin_token_')) {
    req.admin = {
      id: 'admin_001',
      username: 'admin',
      role: 'super_admin'
    };
    next();
  } else {
    return res.status(401).json({
      success: false,
      message: '无效的认证令牌'
    });
  }
};

// 管理员登录
server.post('/api/admin/login', (req, res) => {
  const { username, password } = req.body;

  // 简单的管理员验证（实际项目中应该使用数据库和加密）
  if (username === 'admin' && password === 'admin123') {
    const token = 'mock_admin_token_' + Date.now();
    const user = {
      id: 'admin_001',
      username: 'admin',
      email: '<EMAIL>',
      role: 'super_admin',
      avatar: '',
      createdAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString()
    };

    res.json({
      success: true,
      data: {
        token,
        user
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: '用户名或密码错误'
    });
  }
});

// 管理员登出
server.post('/api/admin/logout', verifyAdmin, (req, res) => {
  res.json({
    success: true,
    message: '登出成功'
  });
});

// 检查管理员token
server.get('/api/admin/check-token', verifyAdmin, (req, res) => {
  res.json({
    success: true,
    data: req.admin
  });
});

// 获取管理员资料
server.get('/api/admin/profile', verifyAdmin, (req, res) => {
  res.json({
    success: true,
    data: req.admin
  });
});

// 获取仪表板数据
server.get('/api/dashboard', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));

  // 统计数据
  const totalUsers = db.users?.length || 0;
  const totalNovels = db.novels?.length || 0;
  const memberUsers = db.users?.filter(u => u.isMember)?.length || 0;
  const todaySync = db.syncData?.filter(s => {
    const today = new Date().toDateString();
    return new Date(s.timestamp).toDateString() === today;
  })?.length || 0;

  res.json({
    success: true,
    data: {
      stats: {
        totalUsers,
        totalNovels,
        memberUsers,
        todaySync
      },
      recentUsers: db.users?.slice(-5) || [],
      popularNovels: db.novels?.slice(-5) || []
    }
  });
});

// 获取用户列表（管理员）
server.get('/api/users', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const { page = 1, size = 20, keyword = '', memberType = '' } = req.query;

  let users = db.users || [];

  // 搜索过滤
  if (keyword) {
    users = users.filter(u =>
      u.username?.includes(keyword) ||
      u.phoneNumber?.includes(keyword) ||
      u.email?.includes(keyword)
    );
  }

  // 会员类型过滤
  if (memberType) {
    users = users.filter(u => u.membershipType === memberType);
  }

  // 分页
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const paginatedUsers = users.slice(start, end);

  res.json({
    success: true,
    data: {
      users: paginatedUsers,
      total: users.length,
      page: parseInt(page),
      size: parseInt(size)
    }
  });
});

// 获取会员码列表（管理员）
server.get('/api/member-codes', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const { page = 1, size = 20, keyword = '', status = '' } = req.query;

  let memberCodes = db.memberCodes || [];

  // 搜索过滤
  if (keyword) {
    memberCodes = memberCodes.filter(c => c.code?.includes(keyword));
  }

  // 状态过滤
  if (status === 'used') {
    memberCodes = memberCodes.filter(c => c.isUsed);
  } else if (status === 'unused') {
    memberCodes = memberCodes.filter(c => !c.isUsed);
  } else if (status === 'expired') {
    memberCodes = memberCodes.filter(c =>
      c.expireAt && new Date(c.expireAt) < new Date()
    );
  }

  // 分页
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const paginatedCodes = memberCodes.slice(start, end);

  res.json({
    success: true,
    data: {
      memberCodes: paginatedCodes,
      total: memberCodes.length,
      page: parseInt(page),
      size: parseInt(size)
    }
  });
});

// 获取小说列表（管理员）
server.get('/api/novels', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const { page = 1, size = 20, keyword = '', genre = '', status = '' } = req.query;

  let novels = db.novels || [];

  // 搜索过滤
  if (keyword) {
    novels = novels.filter(n =>
      n.title?.includes(keyword) ||
      n.author?.includes(keyword)
    );
  }

  // 类型过滤
  if (genre) {
    novels = novels.filter(n => n.genre === genre);
  }

  // 状态过滤
  if (status) {
    novels = novels.filter(n => n.status === status);
  }

  // 分页
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const paginatedNovels = novels.slice(start, end);

  res.json({
    success: true,
    data: {
      novels: paginatedNovels,
      total: novels.length,
      page: parseInt(page),
      size: parseInt(size)
    }
  });
});

// 获取小说详情（管理员）
server.get('/api/novels/:id', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const novelId = req.params.id;

  const novel = db.novels?.find(n => n.id === novelId);

  if (!novel) {
    return res.status(404).json({
      success: false,
      message: '小说不存在'
    });
  }

  res.json({
    success: true,
    data: novel
  });
});

// 获取数据同步记录（管理员）
server.get('/api/sync/records', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const { page = 1, size = 20, keyword = '', status = '' } = req.query;

  let syncRecords = db.syncData || [];

  // 搜索过滤
  if (keyword) {
    syncRecords = syncRecords.filter(s =>
      s.userId?.includes(keyword) ||
      s.username?.includes(keyword)
    );
  }

  // 状态过滤
  if (status) {
    syncRecords = syncRecords.filter(s => s.status === status);
  }

  // 分页
  const start = (page - 1) * size;
  const end = start + parseInt(size);
  const paginatedRecords = syncRecords.slice(start, end);

  res.json({
    success: true,
    data: {
      syncRecords: paginatedRecords,
      total: syncRecords.length,
      page: parseInt(page),
      size: parseInt(size)
    }
  });
});

// 获取会员统计（管理员）
server.get('/api/members/stats', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const users = db.users || [];

  const totalMembers = users.filter(u => u.isMember).length;
  const permanentMembers = users.filter(u => u.membershipType === 'permanent').length;
  const monthlyMembers = users.filter(u => u.membershipType === 'monthly').length;
  const conversionRate = users.length > 0 ? ((totalMembers / users.length) * 100).toFixed(1) : 0;

  res.json({
    success: true,
    data: {
      totalMembers,
      permanentMembers,
      monthlyMembers,
      conversionRate: parseFloat(conversionRate)
    }
  });
});

// 获取小说统计（管理员）
server.get('/api/novels/stats', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const novels = db.novels || [];

  const total = novels.length;
  const totalWords = novels.reduce((sum, n) => sum + (n.wordCount || 0), 0);
  const todayCreated = novels.filter(n => {
    const today = new Date().toDateString();
    return new Date(n.createdAt).toDateString() === today;
  }).length;
  const avgWords = total > 0 ? Math.round(totalWords / total) : 0;

  res.json({
    success: true,
    data: {
      total,
      totalWords,
      todayCreated,
      avgWords
    }
  });
});

// 获取数据同步统计（管理员）
server.get('/api/sync/stats', verifyAdmin, (req, res) => {
  const db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
  const syncRecords = db.syncData || [];

  const totalSyncs = syncRecords.length;
  const successCount = syncRecords.filter(s => s.status === 'success').length;
  const successRate = totalSyncs > 0 ? ((successCount / totalSyncs) * 100).toFixed(1) : 100;
  const todayCount = syncRecords.filter(s => {
    const today = new Date().toDateString();
    return new Date(s.timestamp).toDateString() === today;
  }).length;
  const totalDataSize = syncRecords.reduce((sum, s) => sum + (s.dataSize || 0), 0);

  res.json({
    success: true,
    data: {
      totalSyncs,
      successRate: parseFloat(successRate),
      todayCount,
      totalDataSize
    }
  });
});

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`🚀 CloudBase Novel App API 服务器已启动`);
  console.log(`📡 服务器地址: http://localhost:${PORT}`);
  console.log(`🔗 API基础路径: http://localhost:${PORT}/api`);
  console.log(`💾 数据库文件: ${dbPath}`);
  console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
  console.log('');
  console.log('🔧 可用的API端点:');
  console.log('   POST /api/auth/register - 用户注册');
  console.log('   POST /api/auth/login - 用户登录');
  console.log('   POST /api/admin/login - 管理员登录');
  console.log('   POST /api/sync/upload - 数据同步上传');
  console.log('   GET  /api/sync/download - 数据同步下载');
  console.log('   GET  /api/dashboard - 管理仪表板');
  console.log('   GET  /api/users - 用户管理');
  console.log('   GET  /api/novels - 小说管理');
  console.log('   GET  /api/member-codes - 会员码管理');
  console.log('   GET  /api/sync/records - 数据同步记录');
  console.log('   GET  /api/members/stats - 会员统计');
  console.log('   GET  /api/novels/stats - 小说统计');
  console.log('   GET  /api/sync/stats - 同步统计');
  console.log('   GET  /api/health - 健康检查');
  console.log('');
});

// 导出服务器实例
module.exports = server;
