<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
        .error {
            color: #ff4d4f;
        }
        .success {
            color: #52c41a;
        }
    </style>
</head>
<body>
    <h1>🔧 API测试工具</h1>
    
    <div class="test-section">
        <h3>1. 管理员登录测试</h3>
        <button onclick="testLogin()">测试登录</button>
        <div id="loginResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 仪表板API测试</h3>
        <button onclick="testDashboard()">测试仪表板</button>
        <div id="dashboardResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 用户API测试</h3>
        <button onclick="testUsers()">测试用户列表</button>
        <div id="usersResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 小说API测试</h3>
        <button onclick="testNovels()">测试小说列表</button>
        <button onclick="testNovelStats()">测试小说统计</button>
        <div id="novelsResult" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>5. 会员码API测试</h3>
        <button onclick="testMemberCodes()">测试会员码</button>
        <div id="memberCodesResult" class="result"></div>
    </div>

    <script>
        let adminToken = '';

        async function testLogin() {
            const resultDiv = document.getElementById('loginResult');
            resultDiv.textContent = '正在测试登录...';
            
            try {
                const response = await fetch('/api/admin/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    adminToken = result.data.token;
                    resultDiv.innerHTML = `<span class="success">✅ 登录成功</span>\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 登录失败</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testDashboard() {
            const resultDiv = document.getElementById('dashboardResult');
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取token</span>';
                return;
            }
            
            resultDiv.textContent = '正在测试仪表板API...';
            
            try {
                const response = await fetch('/api/dashboard', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 仪表板API正常</span>\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 仪表板API失败 (${response.status})</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testUsers() {
            const resultDiv = document.getElementById('usersResult');
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取token</span>';
                return;
            }
            
            resultDiv.textContent = '正在测试用户API...';
            
            try {
                const response = await fetch('/api/users?page=1&size=5', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 用户API正常</span>\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 用户API失败 (${response.status})</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testNovels() {
            const resultDiv = document.getElementById('novelsResult');
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取token</span>';
                return;
            }
            
            resultDiv.textContent = '正在测试小说API...';
            
            try {
                const response = await fetch('/api/novels?page=1&size=5', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 小说API正常</span>\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 小说API失败 (${response.status})</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testNovelStats() {
            const resultDiv = document.getElementById('novelsResult');
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取token</span>';
                return;
            }
            
            resultDiv.textContent = '正在测试小说统计API...';
            
            try {
                const response = await fetch('/api/novels/stats', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 小说统计API正常</span>\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 小说统计API失败 (${response.status})</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }

        async function testMemberCodes() {
            const resultDiv = document.getElementById('memberCodesResult');
            
            if (!adminToken) {
                resultDiv.innerHTML = '<span class="error">❌ 请先登录获取token</span>';
                return;
            }
            
            resultDiv.textContent = '正在测试会员码API...';
            
            try {
                const response = await fetch('/api/member-codes?page=1&size=5', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.innerHTML = `<span class="success">✅ 会员码API正常</span>\n${JSON.stringify(result, null, 2)}`;
                } else {
                    resultDiv.innerHTML = `<span class="error">❌ 会员码API失败 (${response.status})</span>\n${JSON.stringify(result, null, 2)}`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 网络错误</span>\n${error.message}`;
            }
        }
    </script>
</body>
</html>
