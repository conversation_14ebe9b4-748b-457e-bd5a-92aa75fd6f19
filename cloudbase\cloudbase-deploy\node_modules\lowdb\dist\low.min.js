/*! lowdb v1.0.0 */
var low=function(t){function e(n){if(r[n])return r[n].exports;var o=r[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,e),o.l=!0,o.exports}var r={};return e.m=t,e.c=r,e.d=function(t,r,n){e.o(t,r)||Object.defineProperty(t,r,{configurable:!1,enumerable:!0,get:n})},e.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(r,"a",r),r},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=0)}([function(t,e,r){"use strict";var n=r(1),o=r(2);t.exports=function(t){function e(t){return u.__wrapped__=t,u}if("object"!=typeof t)throw new Error("An adapter must be provided, see https://github.com/typicode/lowdb/#usage");var r=n.runInContext(),u=r.chain({});return r.prototype.write=r.wrap(r.prototype.value,function(t){var e=t.apply(this);return u.write(e)}),u._=r,u.read=function(){var r=t.read();return o(r)?r.then(e):e(r)},u.write=function(e){var r=t.write(u.getState());return o(r)?r.then(function(){return e}):e},u.getState=function(){return u.__wrapped__},u.setState=function(t){return e(t)},u.read()}},function(t,e){t.exports=_},function(t,e){function r(t){return!!t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof t.then}t.exports=r}]);