# 将本地Mock服务器部署到腾讯云CloudBase的详细教程

## 一、准备工作

### 1. 注册腾讯云账号
1. 访问腾讯云官网 https://cloud.tencent.com/ 注册并登录账号
2. 完成实名认证（如果尚未完成）

### 2. 开通CloudBase服务
1. 在腾讯云控制台搜索"云开发CloudBase"或直接访问 https://console.cloud.tencent.com/tcb
2. 点击"立即开通"，选择"按量计费"或适合您的套餐

### 3. 创建CloudBase环境
1. 点击"新建环境"
2. 填写环境名称（如"novel-app-mock"）
3. 选择计费模式和地域（建议选择离您用户较近的地域）
4. 点击"立即创建"

### 4. 安装必要工具
1. 安装Node.js（如果尚未安装）：https://nodejs.org/
2. 安装CloudBase CLI：
```bash
npm install -g @cloudbase/cli
```

## 二、准备项目文件

### 1. 创建CloudBase项目目录
1. 在您的电脑上创建一个新目录，如`novel-app-cloudbase`
2. 将以下文件复制到这个目录中：
   - `cloudbase-server.js` (已创建)
   - `index.js` (已创建)
   - `cloudbase-package.json` (重命名为 package.json)
   - `cloudbaserc.json` (已创建)
   - `db.json` (从原始mock-server目录复制)

### 2. 文件说明
- `cloudbase-server.js`: 适配CloudBase环境的服务器代码
- `index.js`: 云函数入口文件
- `package.json`: 项目依赖配置
- `cloudbaserc.json`: CloudBase配置文件
- `db.json`: 数据库文件

## 三、部署到CloudBase

### 1. 安装项目依赖
在项目目录中运行：
```bash
npm install
```

### 2. 登录CloudBase
```bash
tcb login
```
按照提示完成登录过程。

### 3. 初始化CloudBase项目
```bash
tcb init
```
在初始化过程中，选择您之前创建的环境ID。

### 4. 修改配置文件
编辑`cloudbaserc.json`文件，将`envId`替换为您的实际环境ID。

### 5. 部署云函数
```bash
tcb fn deploy novel-app-api
```

### 6. 部署API网关
```bash
tcb service create -p /api -f novel-app-api
```

### 7. 设置API访问路径
1. 在CloudBase控制台中，进入您的环境
2. 点击"云函数"，找到`novel-app-api`函数
3. 点击"触发管理"，添加HTTP触发
4. 路径设置为`/api`，勾选"启用"
5. 点击"确定"保存

## 四、测试部署

### 1. 获取API访问地址
1. 在CloudBase控制台中，进入您的环境
2. 点击"云函数"，找到`novel-app-api`函数
3. 点击"触发管理"，查看HTTP触发的访问路径
4. 完整的API地址应该是：`https://您的环境ID.service.tcloudbase.com/api`

### 2. 测试API
使用Postman或其他API测试工具测试以下接口：

1. 登录接口：
```
POST https://您的环境ID.service.tcloudbase.com/api/auth/login
Content-Type: application/json

{
  "username": "testuser",
  "password": "9f86d081884c7d659a2feaa0c55ad015a3bf4f1b2b0b822cd15d6c15b0f00a08"
}
```

2. 获取会员套餐：
```
GET https://您的环境ID.service.tcloudbase.com/api/packages
```

## 五、修改Flutter应用连接到云端API

### 1. 更新API配置
在您的Flutter应用中，找到API配置文件（通常是`lib/config/api_config.dart`或类似文件），将API基础URL更新为CloudBase的URL：

```dart
class ApiConfig {
  // 修改为您的CloudBase API地址
  static const String baseUrl = 'https://您的环境ID.service.tcloudbase.com/api';
  
  // 其他配置保持不变
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  // ... 其他端点
}
```

## 六、注意事项

1. **环境ID**: 请确保在所有配置文件中使用正确的环境ID
2. **数据持久化**: CloudBase环境中的文件系统是临时的，建议使用CloudBase数据库进行数据持久化
3. **日志查看**: 可以在CloudBase控制台的云函数页面查看函数执行日志
4. **性能优化**: 根据实际使用情况调整云函数的内存和超时配置
5. **安全性**: 在生产环境中，请修改JWT密钥和其他敏感配置

## 七、故障排除

1. **部署失败**: 检查网络连接和权限设置
2. **函数执行超时**: 增加函数超时时间配置
3. **依赖安装失败**: 确保Node.js版本兼容
4. **API访问失败**: 检查CORS配置和API路径

## 八、后续优化建议

1. 使用CloudBase数据库替代JSON文件存储
2. 集成真实的短信验证服务
3. 添加更完善的错误处理和日志记录
4. 实现真实的支付接口集成
5. 添加API访问频率限制和安全防护
