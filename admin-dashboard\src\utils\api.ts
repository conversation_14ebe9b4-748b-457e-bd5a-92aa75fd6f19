import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('admin_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    console.error('API请求错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          localStorage.removeItem('admin_token')
          localStorage.removeItem('admin_user')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const userApi = {
  // 获取用户列表
  getUsers: (params?: any) => api.get('/users', { params }),
  
  // 获取用户详情
  getUserById: (id: string) => api.get(`/users/${id}`),
  
  // 更新用户信息
  updateUser: (id: string, data: any) => api.put(`/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id: string) => api.delete(`/users/${id}`),
  
  // 批量删除用户
  batchDeleteUsers: (ids: string[]) => api.post('/users/batch-delete', { ids }),
  
  // 获取用户统计
  getUserStats: () => api.get('/users/stats')
}

export const novelApi = {
  // 获取小说列表
  getNovels: (params?: any) => api.get('/novels', { params }),
  
  // 获取小说详情
  getNovelById: (id: string) => api.get(`/novels/${id}`),
  
  // 删除小说
  deleteNovel: (id: string) => api.delete(`/novels/${id}`),
  
  // 获取小说统计
  getNovelStats: () => api.get('/novels/stats')
}

export const memberApi = {
  // 获取会员码列表
  getMemberCodes: (params?: any) => api.get('/member-codes', { params }),
  
  // 生成会员码
  generateMemberCodes: (data: any) => api.post('/member-codes/generate', data),
  
  // 删除会员码
  deleteMemberCode: (id: string) => api.delete(`/member-codes/${id}`),
  
  // 获取会员统计
  getMemberStats: () => api.get('/members/stats')
}

export const syncApi = {
  // 获取同步记录
  getSyncRecords: (params?: any) => api.get('/sync/records', { params }),
  
  // 获取同步统计
  getSyncStats: () => api.get('/sync/stats')
}

export const dashboardApi = {
  // 获取仪表板数据
  getDashboardData: () => api.get('/dashboard'),
  
  // 获取系统状态
  getSystemStatus: () => api.get('/system/status')
}

export { api }
