import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:crypto/crypto.dart';

/// 网络连接测试工具
/// 用于测试Flutter应用与服务器的连接
void main() async {
  print('🌐 网络连接测试工具');
  print('=' * 50);
  
  final serverUrl = 'http://192.168.1.126:3001';
  final apiUrl = '$serverUrl/api';
  
  // 1. 测试基础连接
  await testBasicConnection(serverUrl);
  
  // 2. 测试API连接
  await testApiConnection(apiUrl);
  
  // 3. 测试登录API
  await testLoginApi(apiUrl);
  
  // 4. 使用Dio测试（模拟Flutter应用）
  await testWithDio(apiUrl);
}

/// 测试基础连接
Future<void> testBasicConnection(String serverUrl) async {
  print('\n📡 测试基础连接...');
  try {
    final client = HttpClient();
    client.connectionTimeout = const Duration(seconds: 10);
    
    final request = await client.getUrl(Uri.parse(serverUrl));
    final response = await request.close();
    
    print('✅ 基础连接成功');
    print('   状态码: ${response.statusCode}');
    print('   服务器: ${response.headers.value('server') ?? '未知'}');
    
    client.close();
  } catch (e) {
    print('❌ 基础连接失败: $e');
  }
}

/// 测试API连接
Future<void> testApiConnection(String apiUrl) async {
  print('\n🔌 测试API连接...');
  try {
    final client = HttpClient();
    client.connectionTimeout = const Duration(seconds: 10);
    
    final request = await client.getUrl(Uri.parse(apiUrl));
    final response = await request.close();
    
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('✅ API连接成功');
    print('   状态码: ${response.statusCode}');
    print('   响应: ${responseBody.length > 100 ? responseBody.substring(0, 100) + '...' : responseBody}');
    
    client.close();
  } catch (e) {
    print('❌ API连接失败: $e');
  }
}

/// 测试登录API
Future<void> testLoginApi(String apiUrl) async {
  print('\n🔐 测试登录API...');
  try {
    final client = HttpClient();
    client.connectionTimeout = const Duration(seconds: 10);
    
    final loginData = {
      'username': 'wblx7',
      'password': _hashPassword('hello'),
    };
    
    final request = await client.postUrl(Uri.parse('$apiUrl/auth/login'));
    request.headers.set('Content-Type', 'application/json');
    request.write(jsonEncode(loginData));
    
    final response = await request.close();
    final responseBody = await response.transform(utf8.decoder).join();
    
    print('✅ 登录API测试完成');
    print('   状态码: ${response.statusCode}');
    print('   响应: ${responseBody.length > 200 ? responseBody.substring(0, 200) + '...' : responseBody}');
    
    client.close();
  } catch (e) {
    print('❌ 登录API测试失败: $e');
  }
}

/// 使用Dio测试（模拟Flutter应用）
Future<void> testWithDio(String apiUrl) async {
  print('\n📱 使用Dio测试（模拟Flutter应用）...');
  try {
    final dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 60),
      receiveTimeout: const Duration(seconds: 60),
      sendTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));
    
    // 添加日志拦截器
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: false,
      responseHeader: false,
      error: true,
      logPrint: (obj) => print('DIO: $obj'),
    ));
    
    print('发送登录请求...');
    print('URL: $apiUrl/auth/login');
    print('用户名: wblx7');
    print('密码哈希: ${_hashPassword('hello')}');
    
    final response = await dio.post(
      '$apiUrl/auth/login',
      data: {
        'username': 'wblx7',
        'password': _hashPassword('hello'),
      },
    );
    
    print('✅ Dio登录测试成功');
    print('   状态码: ${response.statusCode}');
    print('   成功: ${response.data['success']}');
    if (response.data['success'] == true) {
      print('   用户: ${response.data['data']['user']['username']}');
      print('   Token: ${response.data['data']['token']?.substring(0, 20)}...');
    }
    
  } catch (e) {
    print('❌ Dio测试失败: $e');
    if (e is DioException) {
      print('   错误类型: ${e.type}');
      print('   错误信息: ${e.message}');
      if (e.response != null) {
        print('   响应状态码: ${e.response?.statusCode}');
        print('   响应数据: ${e.response?.data}');
      }
    }
  }
}

/// 密码哈希（与Flutter应用保持一致）
String _hashPassword(String password) {
  final bytes = utf8.encode(password);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
