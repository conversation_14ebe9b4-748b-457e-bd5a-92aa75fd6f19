import { defineStore } from 'pinia'
import { ref } from 'vue'
import { api } from '@/utils/api'

interface AdminUser {
  id: string
  username: string
  email: string
  role: string
  avatar?: string
  createdAt: string
  lastLoginAt?: string
}

export const useAuthStore = defineStore('auth', () => {
  const token = ref<string | null>(localStorage.getItem('admin_token'))
  const user = ref<AdminUser | null>(null)
  const isLoggedIn = computed(() => !!token.value)

  // 登录
  const login = async (username: string, password: string) => {
    try {
      const response = await api.post('/admin/login', {
        username,
        password
      })

      if (response.data.success) {
        token.value = response.data.data.token
        user.value = response.data.data.user
        
        localStorage.setItem('admin_token', token.value!)
        localStorage.setItem('admin_user', JSON.stringify(user.value))
        
        return { success: true }
      } else {
        return { success: false, message: response.data.message }
      }
    } catch (error: any) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败，请稍后重试' 
      }
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await api.post('/admin/logout')
      }
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      token.value = null
      user.value = null
      localStorage.removeItem('admin_token')
      localStorage.removeItem('admin_user')
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await api.get('/admin/profile')
      if (response.data.success) {
        user.value = response.data.data
        localStorage.setItem('admin_user', JSON.stringify(user.value))
        return true
      }
      return false
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return false
    }
  }

  // 初始化认证状态
  const initAuth = () => {
    const savedUser = localStorage.getItem('admin_user')
    if (savedUser && token.value) {
      try {
        user.value = JSON.parse(savedUser)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        logout()
      }
    }
  }

  // 检查token有效性
  const checkTokenValid = async () => {
    if (!token.value) return false
    
    try {
      const response = await api.get('/admin/check-token')
      return response.data.success
    } catch (error) {
      console.error('Token验证失败:', error)
      logout()
      return false
    }
  }

  return {
    token,
    user,
    isLoggedIn,
    login,
    logout,
    getUserInfo,
    initAuth,
    checkTokenValid
  }
})
