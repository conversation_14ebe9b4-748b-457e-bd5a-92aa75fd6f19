import 'dart:convert';
import 'package:http/http.dart' as http;

/// 测试CloudBase API集成
class CloudBaseAPITester {
  final String _envId = 'novel-app-2gywkgnn15cbd6a8';
  final String _baseUrl = 'https://tcb-api.tencentcloudapi.com/web';
  
  /// 测试获取上传信息API
  Future<void> testGetUploadInfo() async {
    print('🧪 开始测试CloudBase获取上传信息API...');
    
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl?env=$_envId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test_token', // 使用测试token
        },
        body: jsonEncode({
          'action': 'storage.getUploadMetadata',
          'dataVersion': '2019-04-09',
          'path': 'test-file.json',
        }),
      );
      
      print('📊 响应状态码: ${response.statusCode}');
      print('📄 响应内容: ${response.body}');
      
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('✅ API调用成功');
        print('📋 响应数据结构: ${data.keys}');
      } else {
        print('❌ API调用失败');
      }
      
    } catch (e) {
      print('❌ API调用异常: $e');
    }
  }
  
  /// 测试不同的API端点
  Future<void> testDifferentEndpoints() async {
    print('🧪 测试不同的API端点...');
    
    final endpoints = [
      'https://tcb-api.tencentcloudapi.com/web?env=$_envId',
      'https://tcb-api.tencentcloudapi.com/v1/storages/get-objects-upload-info',
      'https://api.cloudbase.net/v1/storages/get-objects-upload-info',
    ];
    
    for (final endpoint in endpoints) {
      print('\n🔗 测试端点: $endpoint');
      
      try {
        final response = await http.post(
          Uri.parse(endpoint),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test_token',
            if (endpoint.contains('tcb-api.tencentcloudapi.com/web')) ...{
              // Web API格式
            } else ...{
              'X-CloudBase-Env': _envId,
            }
          },
          body: jsonEncode(
            endpoint.contains('tcb-api.tencentcloudapi.com/web') 
              ? {
                  'action': 'storage.getUploadMetadata',
                  'dataVersion': '2019-04-09',
                  'path': 'test-file.json',
                }
              : [
                  {
                    'objectId': 'test-file.json',
                  }
                ]
          ),
        );
        
        print('   状态码: ${response.statusCode}');
        print('   响应: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');
        
      } catch (e) {
        print('   异常: $e');
      }
    }
  }
  
  /// 运行所有测试
  Future<void> runAllTests() async {
    print('🚀 开始CloudBase API测试...\n');
    
    await testGetUploadInfo();
    print('\n' + '='*50 + '\n');
    await testDifferentEndpoints();
    
    print('\n🏁 测试完成！');
  }
}

/// 主函数
void main() async {
  final tester = CloudBaseAPITester();
  await tester.runAllTests();
}
