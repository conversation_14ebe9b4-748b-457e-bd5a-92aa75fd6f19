import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:crypto/crypto.dart';
import 'package:local_auth/local_auth.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'user_sync_service.dart';

/// 用户认证服务
class AuthService extends GetxService {
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_data';
  static const String _biometricEnabledKey = 'biometric_enabled';

  late final Dio _dio;
  final LocalAuthentication _localAuth = LocalAuthentication();
  
  final Rx<User?> currentUser = Rx<User?>(null);
  final RxBool isLoggedIn = false.obs;
  final RxBool isLoading = false.obs;

  SharedPreferences? _prefs;

  @override
  Future<void> onInit() async {
    super.onInit();
    _prefs = await SharedPreferences.getInstance();
    _initializeDio();
    await _initializeAuth();
  }

  /// 初始化Dio配置
  void _initializeDio() {
    _dio = Dio(BaseOptions(
      connectTimeout: Duration(seconds: ApiConfig.connectionTimeout),
      receiveTimeout: Duration(seconds: ApiConfig.receiveTimeout),
      sendTimeout: Duration(seconds: ApiConfig.sendTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // 添加日志拦截器用于调试
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      requestHeader: true,
      responseHeader: true,
      error: true,
      logPrint: (obj) => print('DIO: $obj'),
    ));
  }

  /// 初始化认证状态
  Future<void> _initializeAuth() async {
    try {
      final token = _prefs?.getString(_tokenKey);
      if (token != null && !JwtDecoder.isExpired(token)) {
        final userData = _prefs?.getString(_userKey);
        if (userData != null) {
          currentUser.value = User.fromJson(jsonDecode(userData));
          isLoggedIn.value = true;
          _setupDioInterceptors();
        }
      } else {
        await logout();
      }
    } catch (e) {
      print('初始化认证状态失败: $e');
      await logout();
    }
  }

  /// 设置Dio拦截器
  void _setupDioInterceptors() {
    _dio.interceptors.clear();
    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        final token = _prefs?.getString(_tokenKey);
        if (token != null) {
          options.headers['Authorization'] = 'Bearer $token';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        if (error.response?.statusCode == 401) {
          // Token过期，尝试刷新
          final refreshed = await _refreshToken();
          if (refreshed) {
            // 重试原请求
            final options = error.requestOptions;
            final token = _prefs?.getString(_tokenKey);
            options.headers['Authorization'] = 'Bearer $token';
            try {
              final response = await _dio.fetch(options);
              handler.resolve(response);
              return;
            } catch (e) {
              // 重试失败
            }
          }
          // 刷新失败，退出登录
          await logout();
        }
        handler.next(error);
      },
    ));
  }

  /// 发送短信验证码
  Future<bool> sendVerificationCode(String phoneNumber) async {
    try {
      isLoading.value = true;
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/auth/send-code',
        data: {'phoneNumber': phoneNumber},
      );
      return response.data['success'] ?? false;
    } catch (e) {
      print('发送验证码失败: $e');
      Get.snackbar('错误', '发送验证码失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 验证手机号验证码
  Future<bool> verifyCode(String phoneNumber, String code) async {
    try {
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/auth/verify-code',
        data: {
          'phoneNumber': phoneNumber,
          'code': code,
        },
      );
      return response.data['success'] ?? false;
    } catch (e) {
      print('验证码验证失败: $e');
      return false;
    }
  }

  /// 用户注册
  Future<bool> register(RegisterRequest request) async {
    try {
      isLoading.value = true;
      
      // 密码加密
      final hashedPassword = _hashPassword(request.password);
      final requestData = request.toJson();
      requestData['password'] = hashedPassword;

      final response = await _dio.post(
        '${ApiConfig.baseUrl}/auth/register',
        data: requestData,
      );

      if (response.data['success'] == true) {
        // 注册成功，但不自动登录，让用户手动登录
        print('注册成功，用户ID: ${response.data['data']['user']['id']}');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '注册失败');
        return false;
      }
    } catch (e) {
      print('注册失败: $e');
      Get.snackbar('错误', '注册失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 用户登录
  Future<bool> login(String username, String password) async {
    try {
      isLoading.value = true;

      final hashedPassword = _hashPassword(password);
      print('🔐 登录请求详情:');
      print('   用户名: $username');
      print('   密码哈希: $hashedPassword');
      print('   请求URL: ${ApiConfig.baseUrl}/auth/login');

      final response = await _dio.post(
        '${ApiConfig.baseUrl}/auth/login',
        data: {
          'username': username,
          'password': hashedPassword,
        },
      );

      if (response.data['success'] == true) {
        final loginResponse = LoginResponse.fromJson(response.data['data']);
        await _saveAuthData(loginResponse);
        print('登录成功，用户: ${loginResponse.user.username}');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '登录失败');
        return false;
      }
    } catch (e) {
      print('登录失败: $e');
      Get.snackbar('错误', '登录失败: ${e.toString()}');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 生物识别登录
  Future<bool> biometricLogin() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        Get.snackbar('错误', '设备不支持生物识别');
        return false;
      }

      final isEnabled = _prefs?.getBool(_biometricEnabledKey) ?? false;
      if (!isEnabled) {
        Get.snackbar('错误', '未启用生物识别登录');
        return false;
      }

      final authenticated = await _localAuth.authenticate(
        localizedReason: '请验证身份以登录',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (authenticated) {
        // 生物识别成功，使用保存的token登录
        await _initializeAuth();
        return isLoggedIn.value;
      }
      return false;
    } catch (e) {
      print('生物识别登录失败: $e');
      Get.snackbar('错误', '生物识别登录失败');
      return false;
    }
  }

  /// 退出登录
  Future<void> logout() async {
    try {
      // 通知服务器退出登录
      if (isLoggedIn.value) {
        await _dio.post('${ApiConfig.baseUrl}/auth/logout');
      }
    } catch (e) {
      print('服务器退出登录失败: $e');
    }

    // 清除本地数据
    await _prefs?.remove(_tokenKey);
    await _prefs?.remove(_refreshTokenKey);
    await _prefs?.remove(_userKey);
    
    currentUser.value = null;
    isLoggedIn.value = false;
    
    // 清除Dio拦截器
    _dio.interceptors.clear();
  }

  /// 刷新Token
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = _prefs?.getString(_refreshTokenKey);
      if (refreshToken == null) return false;

      final response = await _dio.post(
        '${ApiConfig.baseUrl}/auth/refresh',
        data: {'refreshToken': refreshToken},
      );

      if (response.data['success'] == true) {
        final loginResponse = LoginResponse.fromJson(response.data['data']);
        await _saveAuthData(loginResponse);
        return true;
      }
      return false;
    } catch (e) {
      print('刷新Token失败: $e');
      return false;
    }
  }

  /// 保存认证数据
  Future<void> _saveAuthData(LoginResponse loginResponse) async {
    await _prefs?.setString(_tokenKey, loginResponse.token);
    await _prefs?.setString(_refreshTokenKey, loginResponse.refreshToken);
    await _prefs?.setString(_userKey, jsonEncode(loginResponse.user.toJson()));

    currentUser.value = loginResponse.user;
    isLoggedIn.value = true;

    _setupDioInterceptors();

    // 登录成功后自动触发数据同步下载（仅限会员）
    if (loginResponse.user.isValidMember) {
      print('🔄 登录成功，开始自动同步数据...');
      _triggerAutoSync();
    }
  }

  /// 触发自动数据同步
  void _triggerAutoSync() async {
    try {
      // 延迟一下，确保UI已经更新
      await Future.delayed(const Duration(milliseconds: 1000));

      // 获取同步服务并触发下载
      final syncService = Get.find<UserSyncService>();
      if (syncService.isSyncEnabled.value) {
        print('📥 开始下载云端数据...');
        await syncService.downloadFromCloud();
      }
    } catch (e) {
      print('自动同步失败: $e');
    }
  }

  /// 密码哈希
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 启用/禁用生物识别
  Future<bool> setBiometricEnabled(bool enabled) async {
    if (enabled) {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        Get.snackbar('错误', '设备不支持生物识别');
        return false;
      }

      final authenticated = await _localAuth.authenticate(
        localizedReason: '请验证身份以启用生物识别登录',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (!authenticated) return false;
    }

    await _prefs?.setBool(_biometricEnabledKey, enabled);
    return true;
  }

  /// 检查是否启用生物识别
  bool get isBiometricEnabled => _prefs?.getBool(_biometricEnabledKey) ?? false;
}
