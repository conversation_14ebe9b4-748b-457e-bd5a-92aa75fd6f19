{"name": "typedarray", "version": "0.0.6", "description": "TypedArray polyfill for old browsers", "main": "index.js", "devDependencies": {"tape": "~2.3.2"}, "scripts": {"test": "tape test/*.js test/server/*.js"}, "repository": {"type": "git", "url": "git://github.com/substack/typedarray.git"}, "homepage": "https://github.com/substack/typedarray", "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DataView", "Float32Array", "Float64Array", "Int8Array", "Int16Array", "Int32Array", "Uint8Array", "Uint8ClampedArray", "Uint16Array", "Uint32Array", "typed", "array", "polyfill"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/6..latest", "firefox/16..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}}