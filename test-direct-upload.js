const axios = require('axios');

const BASE_URL = 'https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api';

// 测试新的直传架构
async function testDirectUpload() {
  console.log('=== 测试云存储直传架构 ===');
  console.log(`API地址: ${BASE_URL}`);
  console.log('');

  try {
    const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
    
    // 模拟大量小说数据（超过6MB）
    const testNovels = [];
    for (let i = 1; i <= 20; i++) {
      testNovels.push({
        id: `test_novel_${i.toString().padStart(3, '0')}`,
        title: `测试小说${i}：这是一个很长的标题用来增加数据大小`,
        author: `测试作者${i}`,
        content: '这是测试内容。'.repeat(1000), // 每本小说约15KB内容
        chapters: Array.from({length: 5}, (_, j) => ({
          id: `chapter_${i}_${j+1}`,
          title: `第${j+1}章：测试章节标题`,
          content: '这是章节内容。'.repeat(500), // 每章约7.5KB
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        })),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        tags: ['测试', '科幻', '冒险'],
        description: '这是一个测试小说的描述，用来模拟真实的小说数据结构。'.repeat(10)
      });
    }

    // 计算数据大小
    const testData = {
      novels: testNovels,
      timestamp: new Date().toISOString()
    };
    
    const dataString = JSON.stringify(testData);
    const dataSizeBytes = Buffer.byteLength(dataString, 'utf8');
    const dataSizeMB = dataSizeBytes / (1024 * 1024);
    
    console.log(`📊 测试数据统计:`);
    console.log(`   小说数量: ${testNovels.length} 本`);
    console.log(`   数据大小: ${dataSizeBytes} 字节`);
    console.log(`   数据大小: ${(dataSizeBytes/1024).toFixed(2)} KB`);
    console.log(`   数据大小: ${dataSizeMB.toFixed(2)} MB`);
    
    if (dataSizeMB > 6) {
      console.log(`✅ 数据大小超过6MB，正好测试绕过限制功能`);
    } else {
      console.log(`⚠️ 数据大小未超过6MB，增加测试数据...`);
      // 如果数据不够大，增加更多内容
      testNovels.forEach(novel => {
        novel.content = novel.content.repeat(3);
        novel.chapters.forEach(chapter => {
          chapter.content = chapter.content.repeat(3);
        });
      });
      
      const newDataString = JSON.stringify({novels: testNovels, timestamp: testData.timestamp});
      const newDataSizeMB = Buffer.byteLength(newDataString, 'utf8') / (1024 * 1024);
      console.log(`   增加后数据大小: ${newDataSizeMB.toFixed(2)} MB`);
    }

    console.log('');
    console.log('🚀 开始测试直传到云存储...');

    const response = await axios.post(`${BASE_URL}/sync/upload-direct`, {
      data: {
        novels: testNovels,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 600000 // 10分钟超时
    });

    if (response.data.success) {
      console.log('✅ 云存储直传成功:', response.data.message);
      console.log('   时间戳:', response.data.timestamp);
      console.log('');
      
      // 验证数据是否正确上传
      console.log('📥 验证上传的数据...');
      const downloadResponse = await axios.get(`${BASE_URL}/sync/download`, {
        headers: {
          'Authorization': `Bearer ${vipToken}`
        }
      });

      if (downloadResponse.data.success) {
        const uploadedData = downloadResponse.data.data;
        const uploadedNovels = uploadedData.novels || [];
        
        console.log('✅ 数据验证成功:');
        console.log(`   上传的小说数量: ${uploadedNovels.length} 本`);
        
        if (uploadedNovels.length > 0) {
          console.log('   前几本小说:');
          uploadedNovels.slice(0, 3).forEach((novel, index) => {
            console.log(`   ${index + 1}. ${novel.title}`);
            console.log(`      作者: ${novel.author}`);
            console.log(`      内容长度: ${novel.content ? novel.content.length : 0} 字符`);
            console.log(`      章节数: ${novel.chapters ? novel.chapters.length : 0}`);
          });
          
          if (uploadedNovels.length >= 20) {
            console.log('🎉 成功！所有测试小说都已上传！');
            console.log('🎊 云存储直传架构工作正常，已绕过6MB限制！');
          } else {
            console.log('⚠️ 部分小说可能丢失，请检查上传逻辑');
          }
        } else {
          console.log('❌ 没有找到上传的小说数据');
        }
      } else {
        console.log('❌ 数据验证失败:', downloadResponse.data.message);
      }

    } else {
      console.log('❌ 云存储直传失败:', response.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误详情:', error.response.data);
    }
    
    if (error.code === 'ECONNABORTED') {
      console.error('❌ 请求超时，可能是数据太大或网络问题');
    }
  }
}

// 测试原有的分批上传（对比）
async function testOldBatchUpload() {
  console.log('\n=== 对比测试：原有分批上传 ===');
  
  const vipToken = 'eyJ1c2VySWQiOiJ1c2VyXzE3NTI3NjYyODI2NDlfbTJzdnFjdWVqIiwiZXhwIjoxNzUyODUzNDM5Njg1fQ==';
  
  // 创建一个较大的测试数据（超过6MB）
  const largeTestData = {
    dataType: 'novels',
    data: {
      novels: Array.from({length: 10}, (_, i) => ({
        id: `large_novel_${i}`,
        title: `大型测试小说${i}`,
        content: '这是大量测试内容。'.repeat(50000), // 每本约1MB
        author: '测试作者',
        createdAt: new Date().toISOString()
      }))
    },
    batchInfo: {
      isComplete: true,
      batchId: null,
      batchIndex: 0,
      totalBatches: 1
    },
    timestamp: new Date().toISOString()
  };

  const dataSize = Buffer.byteLength(JSON.stringify(largeTestData), 'utf8') / (1024 * 1024);
  console.log(`📊 测试数据大小: ${dataSize.toFixed(2)} MB`);

  try {
    const response = await axios.post(`${BASE_URL}/sync/upload`, largeTestData, {
      headers: {
        'Authorization': `Bearer ${vipToken}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000
    });

    if (response.data.success) {
      console.log('✅ 原有分批上传成功（意外）');
    } else {
      console.log('❌ 原有分批上传失败（预期）:', response.data.message);
    }
  } catch (error) {
    if (error.response && error.response.status === 413) {
      console.log('✅ 原有方式遇到413错误（预期），证明6MB限制存在');
    } else if (error.response && error.response.status === 500) {
      console.log('✅ 原有方式遇到500错误（预期），可能是数据过大');
    } else {
      console.log('❌ 原有方式遇到其他错误:', error.message);
    }
  }
}

// 运行测试
async function main() {
  await testDirectUpload();
  await testOldBatchUpload();
  
  console.log('\n🎯 总结:');
  console.log('如果云存储直传成功，说明已成功绕过6MB限制');
  console.log('如果原有方式失败，证明6MB限制确实存在');
  console.log('现在Flutter应用应该能够上传大量小说数据了！');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testDirectUpload, testOldBatchUpload };
