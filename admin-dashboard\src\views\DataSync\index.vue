<template>
  <div class="data-sync-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据同步管理</h1>
      <p class="page-subtitle">管理用户数据同步记录和备份</p>
    </div>

    <!-- 同步统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#1890ff"><Refresh /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.totalSyncs }}</div>
            <div class="stat-label">总同步次数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#52c41a"><CircleCheckFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#faad14"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.todayCount }}</div>
            <div class="stat-label">今日同步</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#f5222d"><FolderOpened /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatDataSize(syncStats.totalDataSize) }}</div>
            <div class="stat-label">总数据量</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 搜索和操作栏 -->
    <div class="dashboard-card">
      <div class="search-bar">
        <div class="search-left">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、同步ID"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="searchForm.status"
            placeholder="同步状态"
            style="width: 120px"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="进行中" value="syncing" />
          </el-select>

          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />

          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>

          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>

        <div class="search-right">
          <el-button type="success" @click="handleManualSync">
            <el-icon><Refresh /></el-icon>
            手动同步
          </el-button>

          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>
        </div>
      </div>
    </div>

    <!-- 同步记录表格 -->
    <div class="dashboard-card">
      <el-table
        v-loading="loading"
        :data="syncRecords"
        style="width: 100%"
      >
        <el-table-column prop="id" label="同步ID" width="180" show-overflow-tooltip />

        <el-table-column prop="username" label="用户" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewUser(row.userId)">
              {{ row.username }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="同步内容" width="200">
          <template #default="{ row }">
            <div class="sync-content">
              <el-tag
                v-for="content in row.syncContent"
                :key="content"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ content }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="数据大小" width="100">
          <template #default="{ row }">
            {{ formatDataSize(row.dataSize) }}
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="耗时" width="80">
          <template #default="{ row }">
            {{ row.duration || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="timestamp" label="同步时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.timestamp) }}
          </template>
        </el-table-column>

        <el-table-column label="错误信息" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.error" class="error-message">{{ row.error }}</span>
            <span v-else class="success-message">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>

            <el-button
              v-if="row.status === 'failed'"
              type="warning"
              size="small"
              @click="handleRetry(row)"
            >
              重试
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 同步详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="同步详情"
      width="800px"
    >
      <div v-if="currentRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="同步ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ currentRecord.username }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据大小">{{ formatDataSize(currentRecord.dataSize) }}</el-descriptions-item>
          <el-descriptions-item label="耗时">{{ currentRecord.duration || '-' }}</el-descriptions-item>
          <el-descriptions-item label="同步时间">{{ formatDate(currentRecord.timestamp) }}</el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>同步内容</h4>
          <el-tag
            v-for="content in currentRecord.syncContent"
            :key="content"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ content }}
          </el-tag>
        </div>

        <div v-if="currentRecord.error" style="margin-top: 20px;">
          <h4>错误信息</h4>
          <el-alert
            :title="currentRecord.error"
            type="error"
            :closable="false"
            show-icon
          />
        </div>

        <div v-if="currentRecord.details" style="margin-top: 20px;">
          <h4>详细信息</h4>
          <el-input
            v-model="currentRecord.details"
            type="textarea"
            :rows="6"
            readonly
          />
        </div>
      </div>
    </el-dialog>

    <!-- 手动同步对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="手动同步"
      width="500px"
    >
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="选择用户">
          <el-select v-model="syncForm.userId" placeholder="请选择用户" style="width: 100%">
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="同步内容">
          <el-checkbox-group v-model="syncForm.syncContent">
            <el-checkbox label="小说">小说数据</el-checkbox>
            <el-checkbox label="角色卡片">角色卡片</el-checkbox>
            <el-checkbox label="知识库">知识库文档</el-checkbox>
            <el-checkbox label="用户设置">用户设置</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="syncDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleStartSync" :loading="syncLoading">
          开始同步
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const syncLoading = ref(false)
const detailDialogVisible = ref(false)
const syncDialogVisible = ref(false)
const currentRecord = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  dateRange: null as any
})

// 手动同步表单
const syncForm = reactive({
  userId: '',
  syncContent: ['小说', '角色卡片', '知识库', '用户设置']
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 同步统计
const syncStats = ref({
  totalSyncs: 1247,
  successRate: 98.5,
  todayCount: 89,
  totalDataSize: 2048576000 // 2GB
})

// 用户列表（用于手动同步）
const userList = ref([
  { id: '87ca175d-227c-4afe-95ef-1249b7d7cd3f', username: 'wblx7' },
  { id: '722d89e5-d179-4f80-9b9e-e5c31c1bfa7f', username: 'wblx6' }
])

// 同步记录数据
const syncRecords = ref([
  {
    id: 'sync_1753073509212_001',
    userId: '87ca175d-227c-4afe-95ef-1249b7d7cd3f',
    username: 'wblx7',
    syncContent: ['小说', '角色卡片', '知识库', '用户设置'],
    dataSize: 1048576, // 1MB
    status: 'success',
    duration: '2.3s',
    timestamp: '2025-01-21T12:41:02.737Z',
    error: null,
    details: '同步完成，共处理19个小说文件，13个角色卡片，6个知识库文档'
  },
  {
    id: 'sync_1753073509212_002',
    userId: '722d89e5-d179-4f80-9b9e-e5c31c1bfa7f',
    username: 'wblx6',
    syncContent: ['小说', '用户设置'],
    dataSize: 524288, // 512KB
    status: 'failed',
    duration: '1.8s',
    timestamp: '2025-01-21T11:30:15.456Z',
    error: '网络连接超时',
    details: '同步过程中网络连接中断，建议检查网络状态后重试'
  },
  {
    id: 'sync_1753073509212_003',
    userId: '87ca175d-227c-4afe-95ef-1249b7d7cd3f',
    username: 'wblx7',
    syncContent: ['小说'],
    dataSize: 2097152, // 2MB
    status: 'syncing',
    duration: null,
    timestamp: '2025-01-21T13:15:30.123Z',
    error: null,
    details: '正在同步小说数据...'
  }
])

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'syncing': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    'success': '成功',
    'failed': '失败',
    'syncing': '同步中'
  }
  return textMap[status] || '未知'
}

// 格式化数据大小
const formatDataSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 查看用户
const viewUser = (userId: string) => {
  router.push(`/users/${userId}`)
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadSyncRecords()
}

// 处理重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.dateRange = null
  pagination.page = 1
  loadSyncRecords()
}

// 处理手动同步
const handleManualSync = () => {
  syncDialogVisible.value = true
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 处理查看详情
const handleViewDetail = (record: any) => {
  currentRecord.value = record
  detailDialogVisible.value = true
}

// 处理重试
const handleRetry = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要重试这次同步吗？', '重试确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('重试同步已启动')
    loadSyncRecords()
  } catch (error) {
    // 用户取消操作
  }
}

// 处理开始同步
const handleStartSync = async () => {
  if (!syncForm.userId) {
    ElMessage.warning('请选择要同步的用户')
    return
  }

  if (syncForm.syncContent.length === 0) {
    ElMessage.warning('请选择要同步的内容')
    return
  }

  syncLoading.value = true
  try {
    // 模拟同步过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('同步已启动')
    syncDialogVisible.value = false
    loadSyncRecords()
  } catch (error) {
    ElMessage.error('启动同步失败')
  } finally {
    syncLoading.value = false
  }
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadSyncRecords()
}

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadSyncRecords()
}

// 加载同步记录
const loadSyncRecords = async () => {
  loading.value = true
  try {
    // 这里应该调用API获取同步记录
    await new Promise(resolve => setTimeout(resolve, 500))
    pagination.total = syncRecords.value.length
  } catch (error) {
    ElMessage.error('加载同步记录失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadSyncRecords()
})
</script>

<style scoped>
.data-sync-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sync-content {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.error-message {
  color: #ff4d4f;
}

.success-message {
  color: #d9d9d9;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-left,
  .search-right {
    justify-content: center;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<style scoped>
.data-sync-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 20px 0 12px 0;
}

.coming-soon p {
  color: #8c8c8c;
}
</style>
