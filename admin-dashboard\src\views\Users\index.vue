<template>
  <div class="users-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">用户管理</h1>
      <p class="page-subtitle">管理系统中的所有用户</p>
    </div>

    <!-- 搜索和操作栏 -->
    <div class="dashboard-card">
      <div class="search-bar">
        <div class="search-left">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、手机号"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          
          <el-select
            v-model="searchForm.memberType"
            placeholder="会员类型"
            style="width: 120px"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="普通用户" value="none" />
            <el-option label="月会员" value="monthly" />
            <el-option label="永久会员" value="permanent" />
          </el-select>

          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />

          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>

        <div class="search-right">
          <el-button
            type="danger"
            :disabled="selectedUsers.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除 ({{ selectedUsers.length }})
          </el-button>
          
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 用户表格 -->
    <div class="dashboard-card">
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="username" label="用户名" width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ row.username }}</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="phoneNumber" label="手机号" width="130" />

        <el-table-column prop="email" label="邮箱" width="180" show-overflow-tooltip />

        <el-table-column label="会员状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getMemberTagType(row.membershipType)"
              size="small"
            >
              {{ getMemberTypeText(row.membershipType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="会员到期" width="120">
          <template #default="{ row }">
            <span v-if="row.membershipType === 'permanent'" class="permanent-member">
              永久有效
            </span>
            <span v-else-if="row.memberExpireTime">
              {{ formatDate(row.memberExpireTime) }}
            </span>
            <span v-else class="no-member">-</span>
          </template>
        </el-table-column>

        <el-table-column label="数据同步" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.isDataSyncEnabled"
              @change="handleSyncToggle(row)"
              :disabled="!row.isMember"
            />
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="注册时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column label="最后登录" width="160">
          <template #default="{ row }">
            <span v-if="row.lastLoginAt">
              {{ formatDate(row.lastLoginAt) }}
            </span>
            <span v-else class="never-login">从未登录</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>
            
            <el-button
              type="warning"
              size="small"
              @click="handleEditUser(row)"
            >
              编辑
            </el-button>
            
            <el-popconfirm
              title="确定要删除这个用户吗？"
              @confirm="handleDeleteUser(row)"
            >
              <template #reference>
                <el-button type="danger" size="small">
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 编辑用户对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑用户"
      width="600px"
      @close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="editForm.username" />
        </el-form-item>
        
        <el-form-item label="手机号" prop="phoneNumber">
          <el-input v-model="editForm.phoneNumber" />
        </el-form-item>
        
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="editForm.email" />
        </el-form-item>
        
        <el-form-item label="会员类型" prop="membershipType">
          <el-select v-model="editForm.membershipType" style="width: 100%">
            <el-option label="普通用户" value="none" />
            <el-option label="月会员" value="monthly" />
            <el-option label="永久会员" value="permanent" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="数据同步" prop="isDataSyncEnabled">
          <el-switch v-model="editForm.isDataSyncEnabled" />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveUser" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const saveLoading = ref(false)
const editDialogVisible = ref(false)
const selectedUsers = ref([])
const editFormRef = ref<FormInstance>()

// 搜索表单
const searchForm = reactive({
  keyword: '',
  memberType: '',
  dateRange: null as any
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 用户列表数据
const userList = ref([])

// 编辑表单
const editForm = reactive({
  id: '',
  username: '',
  phoneNumber: '',
  email: '',
  membershipType: 'none',
  isDataSyncEnabled: false
})

// 表单验证规则
const editRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 获取会员类型标签类型
const getMemberTagType = (type: string) => {
  switch (type) {
    case 'permanent': return 'success'
    case 'monthly': return 'warning'
    default: return 'info'
  }
}

// 获取会员类型文本
const getMemberTypeText = (type: string) => {
  switch (type) {
    case 'permanent': return '永久会员'
    case 'monthly': return '月会员'
    default: return '普通用户'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadUserList()
}

// 处理重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.memberType = ''
  searchForm.dateRange = null
  pagination.page = 1
  loadUserList()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedUsers.value = selection
}

// 处理批量删除
const handleBatchDelete = async () => {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要删除的用户')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedUsers.value.length} 个用户吗？此操作将同时删除用户的所有相关数据（小说、同步记录等）。`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const userIds = selectedUsers.value.map(user => user.id)

    const response = await fetch('/api/users/batch-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({ userIds })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      selectedUsers.value = []
      loadUserList()
    } else {
      ElMessage.error(result.message || '批量删除失败')
    }
  } catch (error) {
    if (error.message) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
    // 用户取消操作时不显示错误
  }
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 处理查看详情
const handleViewDetail = (user: any) => {
  router.push(`/users/${user.id}`)
}

// 处理编辑用户
const handleEditUser = (user: any) => {
  Object.assign(editForm, {
    id: user.id,
    username: user.username,
    phoneNumber: user.phoneNumber,
    email: user.email || '',
    membershipType: user.membershipType,
    isDataSyncEnabled: user.isDataSyncEnabled
  })
  editDialogVisible.value = true
}

// 处理删除用户
const handleDeleteUser = async (user: any) => {
  try {
    const response = await fetch(`/api/users/${user.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      loadUserList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    console.error('删除用户失败:', error)
    ElMessage.error('删除失败')
  }
}

// 处理同步开关切换
const handleSyncToggle = async (user: any) => {
  try {
    // 这里应该调用API更新用户同步状态
    ElMessage.success('设置已更新')
  } catch (error) {
    ElMessage.error('设置失败')
    // 恢复原状态
    user.isDataSyncEnabled = !user.isDataSyncEnabled
  }
}

// 处理保存用户
const handleSaveUser = async () => {
  if (!editFormRef.value) return

  try {
    const valid = await editFormRef.value.validate()
    if (!valid) return

    saveLoading.value = true

    const response = await fetch(`/api/users/${editForm.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({
        username: editForm.username,
        email: editForm.email,
        isMember: editForm.isMember,
        membershipType: editForm.membershipType,
        isDataSyncEnabled: editForm.isDataSyncEnabled
      })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message)
      editDialogVisible.value = false
      loadUserList()
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存用户失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 处理编辑对话框关闭
const handleEditDialogClose = () => {
  if (editFormRef.value) {
    editFormRef.value.resetFields()
  }
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadUserList()
}

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadUserList()
}

// 加载用户列表
const loadUserList = async () => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: pagination.page.toString(),
      size: pagination.size.toString(),
      keyword: searchForm.keyword,
      memberType: searchForm.memberType
    })

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.append('startDate', searchForm.dateRange[0])
      params.append('endDate', searchForm.dateRange[1])
    }

    const response = await fetch(`/api/users?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        userList.value = result.data.users || []
        pagination.total = result.data.total || 0
      } else {
        ElMessage.error(result.message || '加载用户列表失败')
      }
    } else {
      ElMessage.error('网络请求失败')
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
    ElMessage.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadUserList()
})
</script>

<style scoped>
.users-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.username {
  font-weight: 500;
}

.permanent-member {
  color: #52c41a;
  font-weight: 500;
}

.no-member {
  color: #d9d9d9;
}

.never-login {
  color: #d9d9d9;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-left,
  .search-right {
    justify-content: center;
  }
}
</style>
