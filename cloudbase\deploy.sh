#!/bin/bash

# 腾讯云CloudBase部署脚本
# 使用方法: ./deploy.sh [环境ID]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ]; then
    print_error "请提供环境ID作为参数"
    echo "使用方法: ./deploy.sh [环境ID]"
    exit 1
fi

ENV_ID=$1

print_message "开始部署到腾讯云CloudBase环境: $ENV_ID"

# 检查必要工具
print_message "检查必要工具..."

if ! command -v node &> /dev/null; then
    print_error "Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm 未安装，请先安装 npm"
    exit 1
fi

if ! command -v tcb &> /dev/null; then
    print_error "CloudBase CLI 未安装，正在安装..."
    npm install -g @cloudbase/cli
fi

# 创建部署目录
DEPLOY_DIR="cloudbase-deploy"
print_message "创建部署目录: $DEPLOY_DIR"

if [ -d "$DEPLOY_DIR" ]; then
    rm -rf "$DEPLOY_DIR"
fi

mkdir -p "$DEPLOY_DIR"

# 复制必要文件
print_message "复制部署文件..."
cp cloudbase-server.js "$DEPLOY_DIR/"
cp index.js "$DEPLOY_DIR/"
cp db.json "$DEPLOY_DIR/"
cp cloudbase-package.json "$DEPLOY_DIR/package.json"

# 更新配置文件
print_message "更新配置文件..."
sed "s/您的环境ID/$ENV_ID/g" cloudbaserc.json > "$DEPLOY_DIR/cloudbaserc.json"

# 进入部署目录
cd "$DEPLOY_DIR"

# 安装依赖
print_message "安装项目依赖..."
npm install

# 检查登录状态
print_message "检查CloudBase登录状态..."
if ! tcb auth list &> /dev/null; then
    print_warning "未登录CloudBase，请先登录"
    tcb login
fi

# 部署云函数
print_message "部署云函数..."
tcb fn deploy novel-app-api --envId "$ENV_ID"

# 创建HTTP服务
print_message "创建HTTP服务..."
tcb service create -p /api -f novel-app-api --envId "$ENV_ID" || print_warning "HTTP服务可能已存在"

# 获取访问地址
print_message "部署完成！"
print_message "API访问地址: https://$ENV_ID.service.tcloudbase.com/api"

print_message "测试API端点:"
echo "  登录: POST https://$ENV_ID.service.tcloudbase.com/api/auth/login"
echo "  注册: POST https://$ENV_ID.service.tcloudbase.com/api/auth/register"
echo "  套餐: GET https://$ENV_ID.service.tcloudbase.com/api/packages"

print_message "请在Flutter应用中更新API配置为上述地址"

# 返回原目录
cd ..

print_message "部署脚本执行完成！"
