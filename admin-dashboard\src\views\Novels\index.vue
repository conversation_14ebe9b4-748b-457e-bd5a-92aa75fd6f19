<template>
  <div class="novels-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">小说管理</h1>
      <p class="page-subtitle">管理系统中的所有小说内容</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#1890ff"><Reading /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ novelStats.total }}</div>
            <div class="stat-label">小说总数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#52c41a"><EditPen /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatWordCount(novelStats.totalWords) }}</div>
            <div class="stat-label">总字数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#faad14"><Star /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ novelStats.todayCreated }}</div>
            <div class="stat-label">今日新增</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="24" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#f5222d"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ novelStats.avgWords }}</div>
            <div class="stat-label">平均字数</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 搜索和操作栏 -->
    <div class="dashboard-card">
      <div class="search-bar">
        <div class="search-left">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索小说标题、作者"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="searchForm.genre"
            placeholder="小说类型"
            style="width: 120px"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="都市" value="都市" />
            <el-option label="玄幻" value="玄幻" />
            <el-option label="科幻" value="科幻" />
            <el-option label="历史" value="历史" />
            <el-option label="其他" value="其他" />
          </el-select>

          <el-select
            v-model="searchForm.status"
            placeholder="状态筛选"
            style="width: 120px"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="连载中" value="ongoing" />
            <el-option label="已完结" value="completed" />
            <el-option label="暂停" value="paused" />
          </el-select>

          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>

          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>

        <div class="search-right">
          <el-button
            type="danger"
            :disabled="selectedNovels.length === 0"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除 ({{ selectedNovels.length }})
          </el-button>

          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 小说表格 -->
    <div class="dashboard-card">
      <el-table
        v-loading="loading"
        :data="novelList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="title" label="小说标题" width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="novel-title">
              <el-button type="text" @click="handleViewDetail(row)">
                {{ row.title }}
              </el-button>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="author" label="作者" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewUser(row.userId)">
              {{ row.author }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column prop="genre" label="类型" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getGenreTagType(row.genre)">
              {{ row.genre }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="字数" width="100">
          <template #default="{ row }">
            {{ formatWordCount(row.wordCount) }}
          </template>
        </el-table-column>

        <el-table-column label="章节数" width="80">
          <template #default="{ row }">
            {{ row.chapterCount || 0 }}
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag size="small" :type="getStatusTagType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="质量评分" width="120">
          <template #default="{ row }">
            <el-rate
              v-model="row.qualityScore"
              :max="5"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}"
            />
          </template>
        </el-table-column>

        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>

        <el-table-column prop="updatedAt" label="最后更新" width="160">
          <template #default="{ row }">
            {{ formatDate(row.updatedAt) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>

            <el-button
              type="warning"
              size="small"
              @click="handleAnalyze(row)"
            >
              分析
            </el-button>

            <el-popconfirm
              title="确定要删除这部小说吗？"
              @confirm="handleDeleteNovel(row)"
            >
              <template #reference>
                <el-button type="danger" size="small">
                  删除
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 小说分析对话框 -->
    <el-dialog
      v-model="analyzeDialogVisible"
      title="小说质量分析"
      width="800px"
    >
      <div v-if="currentNovel">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="小说标题">{{ currentNovel.title }}</el-descriptions-item>
          <el-descriptions-item label="作者">{{ currentNovel.author }}</el-descriptions-item>
          <el-descriptions-item label="类型">{{ currentNovel.genre }}</el-descriptions-item>
          <el-descriptions-item label="字数">{{ formatWordCount(currentNovel.wordCount) }}</el-descriptions-item>
          <el-descriptions-item label="章节数">{{ currentNovel.chapterCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="质量评分">
            <el-rate v-model="currentNovel.qualityScore" disabled show-score />
          </el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>内容分析</h4>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="analysis-item">
                <div class="analysis-label">平均章节字数</div>
                <div class="analysis-value">{{ Math.round(currentNovel.wordCount / (currentNovel.chapterCount || 1)) }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="analysis-item">
                <div class="analysis-label">更新频率</div>
                <div class="analysis-value">{{ getUpdateFrequency(currentNovel) }}</div>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="analysis-item">
                <div class="analysis-label">创作天数</div>
                <div class="analysis-value">{{ getCreationDays(currentNovel) }}天</div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const selectedNovels = ref([])
const analyzeDialogVisible = ref(false)
const currentNovel = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  genre: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 小说统计
const novelStats = ref({
  total: 0,
  totalWords: 0,
  todayCreated: 0,
  avgWords: 0
})

// 小说列表数据
const novelList = ref([])

// 获取类型标签类型
const getGenreTagType = (genre: string) => {
  const typeMap = {
    '科幻': 'primary',
    '历史': 'success',
    '都市': 'warning',
    '玄幻': 'danger',
    '其他': 'info'
  }
  return typeMap[genre] || 'info'
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap = {
    'ongoing': 'success',
    'completed': 'primary',
    'paused': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    'ongoing': '连载中',
    'completed': '已完结',
    'paused': '暂停'
  }
  return textMap[status] || '未知'
}

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 获取更新频率
const getUpdateFrequency = (novel: any) => {
  const days = getCreationDays(novel)
  const chapters = novel.chapterCount || 1
  const frequency = chapters / days
  if (frequency >= 1) {
    return `${frequency.toFixed(1)}章/天`
  } else {
    return `${(1/frequency).toFixed(1)}天/章`
  }
}

// 获取创作天数
const getCreationDays = (novel: any) => {
  const created = dayjs(novel.createdAt)
  const updated = dayjs(novel.updatedAt)
  return updated.diff(created, 'day') + 1
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadNovelList()
}

// 处理重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.genre = ''
  searchForm.status = ''
  pagination.page = 1
  loadNovelList()
}

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedNovels.value = selection
}

// 处理批量删除
const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedNovels.value.length} 部小说吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    ElMessage.success('批量删除成功')
    selectedNovels.value = []
    loadNovelList()
  } catch (error) {
    // 用户取消操作
  }
}

// 处理导出
const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

// 处理查看详情
const handleViewDetail = (novel: any) => {
  router.push(`/novels/${novel.id}`)
}

// 查看用户
const viewUser = (userId: string) => {
  router.push(`/users/${userId}`)
}

// 处理分析
const handleAnalyze = (novel: any) => {
  currentNovel.value = novel
  analyzeDialogVisible.value = true
}

// 处理删除小说
const handleDeleteNovel = async (novel: any) => {
  try {
    ElMessage.success('删除成功')
    loadNovelList()
  } catch (error) {
    ElMessage.error('删除失败')
  }
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadNovelList()
}

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadNovelList()
}

// 加载小说统计
const loadNovelStats = async () => {
  try {
    const response = await fetch('/api/novels/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        novelStats.value = result.data
      }
    }
  } catch (error) {
    console.error('加载小说统计失败:', error)
  }
}

// 加载小说列表
const loadNovelList = async () => {
  loading.value = true
  try {
    const params = new URLSearchParams({
      page: pagination.page.toString(),
      size: pagination.size.toString(),
      keyword: searchForm.keyword,
      genre: searchForm.genre,
      status: searchForm.status
    })

    const response = await fetch(`/api/novels?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        novelList.value = result.data.novels || []
        pagination.total = result.data.total || 0
      } else {
        ElMessage.error(result.message || '加载小说列表失败')
      }
    } else {
      ElMessage.error('网络请求失败')
    }
  } catch (error) {
    console.error('加载小说列表失败:', error)
    ElMessage.error('加载小说列表失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadNovelStats()
  loadNovelList()
})
</script>

<style scoped>
.novels-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.novel-title {
  font-weight: 500;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.analysis-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.analysis-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.analysis-value {
  font-size: 20px;
  font-weight: bold;
  color: #262626;
}

/* 响应式 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-left,
  .search-right {
    justify-content: center;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<style scoped>
.novels-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 20px 0 12px 0;
}

.coming-soon p {
  color: #8c8c8c;
  margin-bottom: 32px;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  max-width: 600px;
  margin: 0 auto;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  color: #52c41a;
}
</style>
