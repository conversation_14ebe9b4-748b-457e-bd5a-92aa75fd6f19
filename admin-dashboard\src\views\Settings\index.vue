<template>
  <div class="settings-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">系统设置</h1>
      <p class="page-subtitle">系统配置和参数管理</p>
    </div>

    <!-- 设置导航 -->
    <div class="dashboard-card">
      <el-tabs v-model="activeTab" type="card">
        <!-- 基础设置 -->
        <el-tab-pane label="基础设置" name="basic">
          <el-form :model="basicSettings" label-width="150px">
            <el-form-item label="系统名称">
              <el-input v-model="basicSettings.systemName" style="width: 300px" />
            </el-form-item>

            <el-form-item label="系统描述">
              <el-input
                v-model="basicSettings.systemDescription"
                type="textarea"
                :rows="3"
                style="width: 500px"
              />
            </el-form-item>

            <el-form-item label="系统版本">
              <el-input v-model="basicSettings.systemVersion" style="width: 200px" readonly />
            </el-form-item>

            <el-form-item label="维护模式">
              <el-switch
                v-model="basicSettings.maintenanceMode"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="setting-desc">开启后，普通用户将无法访问系统</div>
            </el-form-item>

            <el-form-item label="用户注册">
              <el-switch
                v-model="basicSettings.allowRegistration"
                active-text="允许"
                inactive-text="禁止"
              />
              <div class="setting-desc">是否允许新用户注册</div>
            </el-form-item>

            <el-form-item label="默认会员类型">
              <el-select v-model="basicSettings.defaultMemberType" style="width: 200px">
                <el-option label="普通用户" value="none" />
                <el-option label="月会员" value="monthly" />
                <el-option label="永久会员" value="permanent" />
              </el-select>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 数据同步设置 -->
        <el-tab-pane label="数据同步" name="sync">
          <el-form :model="syncSettings" label-width="150px">
            <el-form-item label="自动同步">
              <el-switch
                v-model="syncSettings.autoSync"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="setting-desc">是否自动进行数据同步</div>
            </el-form-item>

            <el-form-item label="同步间隔">
              <el-input-number
                v-model="syncSettings.syncInterval"
                :min="1"
                :max="60"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">分钟</span>
              <div class="setting-desc">自动同步的时间间隔</div>
            </el-form-item>

            <el-form-item label="最大同步大小">
              <el-input-number
                v-model="syncSettings.maxSyncSize"
                :min="1"
                :max="1000"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">MB</span>
              <div class="setting-desc">单次同步的最大数据量</div>
            </el-form-item>

            <el-form-item label="同步重试次数">
              <el-input-number
                v-model="syncSettings.retryCount"
                :min="0"
                :max="10"
                style="width: 200px"
              />
              <div class="setting-desc">同步失败时的重试次数</div>
            </el-form-item>

            <el-form-item label="数据备份">
              <el-switch
                v-model="syncSettings.enableBackup"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="setting-desc">是否启用数据自动备份</div>
            </el-form-item>

            <el-form-item label="备份保留天数">
              <el-input-number
                v-model="syncSettings.backupRetentionDays"
                :min="1"
                :max="365"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">天</span>
              <div class="setting-desc">备份文件的保留时间</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 会员设置 -->
        <el-tab-pane label="会员设置" name="member">
          <el-form :model="memberSettings" label-width="150px">
            <el-form-item label="会员码有效期">
              <el-input-number
                v-model="memberSettings.codeValidityDays"
                :min="1"
                :max="365"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">天</span>
              <div class="setting-desc">会员码的默认有效期</div>
            </el-form-item>

            <el-form-item label="会员码前缀">
              <el-input v-model="memberSettings.codePrefix" style="width: 200px" />
              <div class="setting-desc">生成会员码时的默认前缀</div>
            </el-form-item>

            <el-form-item label="会员码长度">
              <el-input-number
                v-model="memberSettings.codeLength"
                :min="6"
                :max="20"
                style="width: 200px"
              />
              <div class="setting-desc">会员码的字符长度</div>
            </el-form-item>

            <el-form-item label="到期提醒">
              <el-switch
                v-model="memberSettings.expirationReminder"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="setting-desc">是否发送会员到期提醒</div>
            </el-form-item>

            <el-form-item label="提醒提前天数">
              <el-input-number
                v-model="memberSettings.reminderDays"
                :min="1"
                :max="30"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">天</span>
              <div class="setting-desc">提前多少天发送到期提醒</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 安全设置 -->
        <el-tab-pane label="安全设置" name="security">
          <el-form :model="securitySettings" label-width="150px">
            <el-form-item label="密码最小长度">
              <el-input-number
                v-model="securitySettings.minPasswordLength"
                :min="6"
                :max="20"
                style="width: 200px"
              />
              <div class="setting-desc">用户密码的最小长度要求</div>
            </el-form-item>

            <el-form-item label="密码复杂度">
              <el-switch
                v-model="securitySettings.passwordComplexity"
                active-text="开启"
                inactive-text="关闭"
              />
              <div class="setting-desc">是否要求密码包含数字、字母和特殊字符</div>
            </el-form-item>

            <el-form-item label="登录失败限制">
              <el-input-number
                v-model="securitySettings.maxLoginAttempts"
                :min="3"
                :max="10"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">次</span>
              <div class="setting-desc">连续登录失败多少次后锁定账户</div>
            </el-form-item>

            <el-form-item label="账户锁定时间">
              <el-input-number
                v-model="securitySettings.lockoutDuration"
                :min="5"
                :max="60"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">分钟</span>
              <div class="setting-desc">账户锁定的持续时间</div>
            </el-form-item>

            <el-form-item label="Token有效期">
              <el-input-number
                v-model="securitySettings.tokenExpiration"
                :min="1"
                :max="24"
                style="width: 200px"
              />
              <span style="margin-left: 8px;">小时</span>
              <div class="setting-desc">用户登录Token的有效期</div>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 系统监控 -->
        <el-tab-pane label="系统监控" name="monitor">
          <div class="monitor-section">
            <h4>系统状态</h4>
            <el-row :gutter="20">
              <el-col :xs="24" :sm="8" v-for="status in systemStatus" :key="status.name">
                <div class="status-card">
                  <div class="status-icon" :class="status.status">
                    <el-icon size="24">
                      <component :is="status.icon" />
                    </el-icon>
                  </div>
                  <div class="status-content">
                    <div class="status-name">{{ status.name }}</div>
                    <div class="status-value">{{ status.value }}</div>
                    <div class="status-desc">{{ status.description }}</div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>

          <div class="monitor-section">
            <h4>性能指标</h4>
            <el-table :data="performanceMetrics" style="width: 100%">
              <el-table-column prop="metric" label="指标" width="200" />
              <el-table-column prop="current" label="当前值" width="120" />
              <el-table-column prop="average" label="平均值" width="120" />
              <el-table-column prop="peak" label="峰值" width="120" />
              <el-table-column label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getMetricStatus(row.current, row.threshold)" size="small">
                    {{ getMetricStatusText(row.current, row.threshold) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="description" label="说明" show-overflow-tooltip />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 保存按钮 -->
      <div class="settings-actions">
        <el-button @click="handleReset">重置</el-button>
        <el-button type="primary" @click="handleSave" :loading="saveLoading">
          保存设置
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 响应式数据
const activeTab = ref('basic')
const saveLoading = ref(false)

// 基础设置
const basicSettings = reactive({
  systemName: '小说应用后台管理系统',
  systemDescription: '基于Vue 3构建的现代化小说应用管理平台',
  systemVersion: '1.0.0',
  maintenanceMode: false,
  allowRegistration: true,
  defaultMemberType: 'none'
})

// 数据同步设置
const syncSettings = reactive({
  autoSync: true,
  syncInterval: 30,
  maxSyncSize: 100,
  retryCount: 3,
  enableBackup: true,
  backupRetentionDays: 30
})

// 会员设置
const memberSettings = reactive({
  codeValidityDays: 365,
  codePrefix: 'VIP',
  codeLength: 10,
  expirationReminder: true,
  reminderDays: 7
})

// 安全设置
const securitySettings = reactive({
  minPasswordLength: 8,
  passwordComplexity: true,
  maxLoginAttempts: 5,
  lockoutDuration: 15,
  tokenExpiration: 24
})

// 系统状态
const systemStatus = ref([
  {
    name: 'API服务',
    value: '正常运行',
    description: '响应时间: 45ms',
    status: 'success',
    icon: 'CircleCheckFilled'
  },
  {
    name: '数据库',
    value: '连接正常',
    description: '查询时间: 12ms',
    status: 'success',
    icon: 'CircleCheckFilled'
  },
  {
    name: '存储空间',
    value: '78% 已使用',
    description: '剩余: 2.1GB',
    status: 'warning',
    icon: 'WarningFilled'
  }
])

// 性能指标
const performanceMetrics = ref([
  {
    metric: 'CPU使用率',
    current: '45%',
    average: '38%',
    peak: '67%',
    threshold: 80,
    description: '服务器CPU使用情况'
  },
  {
    metric: '内存使用率',
    current: '62%',
    average: '58%',
    peak: '78%',
    threshold: 85,
    description: '服务器内存使用情况'
  },
  {
    metric: 'API响应时间',
    current: '45ms',
    average: '52ms',
    peak: '120ms',
    threshold: 100,
    description: 'API接口平均响应时间'
  },
  {
    metric: '并发用户数',
    current: '89',
    average: '76',
    peak: '156',
    threshold: 200,
    description: '当前在线用户数量'
  }
])

// 获取指标状态
const getMetricStatus = (current: string, threshold: number) => {
  const value = parseFloat(current)
  if (value >= threshold * 0.9) return 'danger'
  if (value >= threshold * 0.7) return 'warning'
  return 'success'
}

// 获取指标状态文本
const getMetricStatusText = (current: string, threshold: number) => {
  const value = parseFloat(current)
  if (value >= threshold * 0.9) return '警告'
  if (value >= threshold * 0.7) return '注意'
  return '正常'
}

// 保存设置
const handleSave = async () => {
  try {
    await ElMessageBox.confirm('确定要保存这些设置吗？', '保存确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    saveLoading.value = true

    // 模拟保存过程
    await new Promise(resolve => setTimeout(resolve, 2000))

    ElMessage.success('设置保存成功')
  } catch (error) {
    // 用户取消操作
  } finally {
    saveLoading.value = false
  }
}

// 重置设置
const handleReset = async () => {
  try {
    await ElMessageBox.confirm('确定要重置所有设置吗？', '重置确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('设置已重置')
  } catch (error) {
    // 用户取消操作
  }
}
</script>

<style scoped>
.settings-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.setting-desc {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
}

.monitor-section {
  margin-bottom: 32px;
}

.monitor-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
}

.status-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.status-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.success {
  background: #f6ffed;
  color: #52c41a;
}

.status-icon.warning {
  background: #fffbe6;
  color: #faad14;
}

.status-icon.danger {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-content {
  flex: 1;
}

.status-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.status-value {
  font-size: 14px;
  color: #595959;
  margin-bottom: 4px;
}

.status-desc {
  font-size: 12px;
  color: #8c8c8c;
}

.settings-actions {
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
  text-align: right;
}

.settings-actions .el-button {
  margin-left: 12px;
}

/* 响应式 */
@media (max-width: 768px) {
  .status-card {
    flex-direction: column;
    text-align: center;
  }

  .settings-actions {
    text-align: center;
  }

  .settings-actions .el-button {
    margin: 0 6px;
  }
}
</style>

<style scoped>
.settings-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 20px 0 12px 0;
}

.coming-soon p {
  color: #8c8c8c;
}
</style>
