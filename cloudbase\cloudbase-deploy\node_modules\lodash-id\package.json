{"name": "lodash-id", "version": "0.14.1", "description": "Use JavaScript objects as databases", "main": "src/index.js", "scripts": {"test": "mocha && standard && pkg-ok", "prepush": "npm test", "prepublish": "npm test"}, "author": "Typicode <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/typicode/lodash-id.git"}, "keywords": ["lodash", "lowdb", "underscore", "id", "resource", "mixin"], "devDependencies": {"husky": "^0.11.8", "lodash": "^4.6.1", "mocha": "^3.2.0", "pkg-ok": "^1.0.1", "sinon": "~1.8.1", "standard": "^8.6.0", "underscore": "^1.8.3", "webpack": "^2.2.1"}, "engines": {"node": ">= 4"}, "standard": {"fix": true, "env": {"mocha": true}}}