import 'dart:convert';
import 'dart:io';

void main() async {
  print('=== Flutter数据同步修复验证 ===');
  
  // 模拟大数据量测试
  final testData = {
    'novels': List.generate(19, (i) => {
      'id': 'novel_${i.toString().padLeft(3, '0')}',
      'title': '测试小说${i + 1}',
      'author': '作者${i + 1}',
      'content': '这是测试内容。' * 100, // 模拟大量内容
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    }),
    'knowledgeDocuments': List.generate(6, (i) => {
      'id': 'doc_${i.toString().padLeft(3, '0')}',
      'title': '知识库文档${i + 1}',
      'content': '这是知识库内容。' * 50,
      'type': 'user',
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    }),
    'characterCards': [],
    'characterTypes': [],
    'stylePackages': [],
    'userSettings': {
      'theme': 'dark',
      'autoSync': true,
      'enableNotification': true,
    }
  };

  // 计算数据大小
  final dataString = jsonEncode(testData);
  final dataSizeKB = dataString.length / 1024;
  print('📊 测试数据大小: ${dataSizeKB.toFixed(2)} KB');

  // 模拟分批逻辑
  print('\n🔄 模拟分批上传逻辑:');
  
  // 1. 小说分批
  final novels = testData['novels'] as List;
  const batchSize = 5;
  final novelBatches = <List>[];
  
  for (int i = 0; i < novels.length; i += batchSize) {
    final end = (i + batchSize < novels.length) ? i + batchSize : novels.length;
    novelBatches.add(novels.sublist(i, end));
  }
  
  print('📚 小说数据: ${novels.length} 本，分为 ${novelBatches.length} 批');
  for (int i = 0; i < novelBatches.length; i++) {
    final batch = novelBatches[i];
    final batchData = {'novels': batch};
    final batchSize = jsonEncode(batchData).length / 1024;
    print('   批次 ${i + 1}: ${batch.length} 本小说, ${batchSize.toFixed(2)} KB');
  }

  // 2. 其他数据类型
  final knowledgeDocuments = testData['knowledgeDocuments'] as List;
  if (knowledgeDocuments.isNotEmpty) {
    final docsData = {'knowledgeDocuments': knowledgeDocuments};
    final docsSize = jsonEncode(docsData).length / 1024;
    print('📖 知识库文档: ${knowledgeDocuments.length} 个, ${docsSize.toFixed(2)} KB');
  }

  final userSettings = testData['userSettings'];
  if (userSettings != null) {
    final settingsData = {'userSettings': userSettings};
    final settingsSize = jsonEncode(settingsData).length / 1024;
    print('⚙️ 用户设置: ${settingsSize.toFixed(2)} KB');
  }

  print('\n✅ 分批上传策略验证完成！');
  print('💡 每个批次的数据大小都远小于1MB限制，应该可以成功上传。');
}

extension on double {
  String toFixed(int digits) {
    return toStringAsFixed(digits);
  }
}
