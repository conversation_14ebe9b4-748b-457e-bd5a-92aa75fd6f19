import 'dart:convert';

void main() {
  print('=== 数据压缩效果测试 ===');
  
  // 模拟原始小说数据
  final originalNovel = {
    'id': 'novel_001',
    'title': '测试小说1',
    'author': '测试作者',
    'content': '这是一个很长的小说内容，包含大量的文字描述和情节发展。' * 50, // 模拟长内容
    'chapters': List.generate(10, (i) => {
      'id': 'chapter_$i',
      'title': '第${i+1}章',
      'content': '这是章节内容，包含详细的情节描述和对话。' * 20, // 模拟章节内容
    }),
    'createdAt': DateTime.now().toIso8601String(),
    'updatedAt': DateTime.now().toIso8601String(),
    'genre': '玄幻',
    'status': 'ongoing',
    'wordCount': 50000,
    'description': '这是小说的详细描述...',
  };

  // 计算原始数据大小
  final originalJson = jsonEncode({'novels': [originalNovel]});
  final originalSizeKB = originalJson.length / 1024;
  
  print('📊 原始数据大小: ${originalSizeKB.toFixed(2)} KB');
  print('   - 内容长度: ${(originalNovel['content'] as String).length} 字符');
  print('   - 章节数量: ${(originalNovel['chapters'] as List).length} 章');
  
  // 应用压缩算法
  final compressedNovel = compressNovel(originalNovel);
  
  // 计算压缩后数据大小
  final compressedJson = jsonEncode({'novels': [compressedNovel]});
  final compressedSizeKB = compressedJson.length / 1024;
  
  print('\n📦 压缩后数据大小: ${compressedSizeKB.toFixed(2)} KB');
  print('   - 内容长度: ${(compressedNovel['content'] as String).length} 字符');
  print('   - 章节数量: ${(compressedNovel['chapters'] as List).length} 章');
  
  final compressionRatio = ((originalSizeKB - compressedSizeKB) / originalSizeKB * 100);
  print('\n✅ 压缩效果: 减少了 ${compressionRatio.toFixed(1)}%');
  
  // 测试知识库文档压缩
  print('\n--- 知识库文档压缩测试 ---');
  
  final originalDoc = {
    'id': 'doc_001',
    'title': '测试文档',
    'content': '这是知识库文档的详细内容，包含大量的参考资料和设定信息。' * 30,
    'type': 'user',
    'tags': ['设定', '角色', '世界观', '剧情', '背景'],
    'createdAt': DateTime.now().toIso8601String(),
    'updatedAt': DateTime.now().toIso8601String(),
    'metadata': {'category': 'character', 'importance': 'high'},
  };
  
  final originalDocJson = jsonEncode({'knowledgeDocuments': [originalDoc]});
  final originalDocSizeKB = originalDocJson.length / 1024;
  
  final compressedDoc = compressKnowledgeDocument(originalDoc);
  final compressedDocJson = jsonEncode({'knowledgeDocuments': [compressedDoc]});
  final compressedDocSizeKB = compressedDocJson.length / 1024;
  
  print('📊 原始文档大小: ${originalDocSizeKB.toFixed(2)} KB');
  print('📦 压缩后大小: ${compressedDocSizeKB.toFixed(2)} KB');
  
  final docCompressionRatio = ((originalDocSizeKB - compressedDocSizeKB) / originalDocSizeKB * 100);
  print('✅ 文档压缩效果: 减少了 ${docCompressionRatio.toFixed(1)}%');
  
  print('\n🎯 结论: 单本小说压缩后约 ${compressedSizeKB.toFixed(1)} KB，远小于CloudBase限制');
}

Map<String, dynamic> compressNovel(Map<String, dynamic> novel) {
  final novelMap = <String, dynamic>{};
  
  // 只保留最关键的字段
  novelMap['id'] = novel['id'];
  novelMap['title'] = novel['title'];
  novelMap['author'] = novel['author'];
  novelMap['createdAt'] = novel['createdAt'];
  novelMap['updatedAt'] = novel['updatedAt'];
  
  // 极度压缩内容字段，只保留前100个字符
  if (novel['content'] is String) {
    final content = novel['content'] as String;
    novelMap['content'] = content.length > 100 
        ? content.substring(0, 100) + '...' 
        : content;
  } else {
    novelMap['content'] = '';
  }
  
  // 压缩章节数据，只保留前3章，每章内容限制50字符
  if (novel['chapters'] is List) {
    final chapters = novel['chapters'] as List;
    final limitedChapters = chapters.take(3).map((chapter) {
      final chapterMap = <String, dynamic>{};
      chapterMap['id'] = chapter['id'];
      chapterMap['title'] = chapter['title'];
      
      if (chapter['content'] is String) {
        final content = chapter['content'] as String;
        chapterMap['content'] = content.length > 50 
            ? content.substring(0, 50) + '...' 
            : content;
      } else {
        chapterMap['content'] = '';
      }
      
      return chapterMap;
    }).toList();
    
    novelMap['chapters'] = limitedChapters;
    if (chapters.length > 3) {
      novelMap['totalChapters'] = chapters.length;
    }
  } else {
    novelMap['chapters'] = [];
  }
  
  return novelMap;
}

Map<String, dynamic> compressKnowledgeDocument(Map<String, dynamic> doc) {
  final docMap = <String, dynamic>{};
  
  // 只保留最关键的字段
  docMap['id'] = doc['id'];
  docMap['title'] = doc['title'];
  docMap['type'] = doc['type'];
  docMap['createdAt'] = doc['createdAt'];
  docMap['updatedAt'] = doc['updatedAt'];
  
  // 极度压缩内容字段，只保留前100个字符
  if (doc['content'] is String) {
    final content = doc['content'] as String;
    docMap['content'] = content.length > 100 
        ? content.substring(0, 100) + '...' 
        : content;
  } else {
    docMap['content'] = '';
  }
  
  // 保留标签但限制数量
  if (doc['tags'] is List) {
    final tags = doc['tags'] as List;
    docMap['tags'] = tags.take(3).toList();
  } else {
    docMap['tags'] = [];
  }
  
  return docMap;
}

extension DoubleExtension on double {
  String toFixed(int digits) => toStringAsFixed(digits);
}
