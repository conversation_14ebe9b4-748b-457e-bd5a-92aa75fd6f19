import { Component } from 'vue';
import type { TooltipInstance } from 'element-plus/es/components/tooltip';
import type { ScrollbarInstance } from 'element-plus/es/components/scrollbar';
import type { SelectEmits, SelectProps } from './select';
import type { OptionBasic, OptionPublicInstance, OptionValue, SelectStates } from './type';
export declare const useSelect: (props: SelectProps, emit: SelectEmits) => {
    inputId: import("vue").Ref<string | undefined>;
    contentId: import("vue").Ref<string>;
    nsSelect: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    nsInput: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string) => string;
        m: (modifier?: string) => string;
        be: (blockSuffix?: string, element?: string) => string;
        em: (element?: string, modifier?: string) => string;
        bm: (blockSuffix?: string, modifier?: string) => string;
        bem: (blockSuffix?: string, element?: string, modifier?: string) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    states: {
        inputValue: string;
        options: Map<import("element-plus/es/utils").EpPropMergeType<(ObjectConstructor | BooleanConstructor | NumberConstructor | StringConstructor)[], unknown, unknown>, OptionPublicInstance> & Omit<Map<import("element-plus/es/utils").EpPropMergeType<(ObjectConstructor | BooleanConstructor | NumberConstructor | StringConstructor)[], unknown, unknown>, OptionPublicInstance>, keyof Map<any, any>>;
        cachedOptions: Map<import("element-plus/es/utils").EpPropMergeType<(ObjectConstructor | BooleanConstructor | NumberConstructor | StringConstructor)[], unknown, unknown>, OptionPublicInstance> & Omit<Map<import("element-plus/es/utils").EpPropMergeType<(ObjectConstructor | BooleanConstructor | NumberConstructor | StringConstructor)[], unknown, unknown>, OptionPublicInstance>, keyof Map<any, any>>;
        optionValues: OptionValue[];
        selected: {
            value: OptionValue;
            currentLabel: OptionPublicInstance["currentLabel"];
            isDisabled?: OptionPublicInstance["isDisabled"] | undefined;
        }[];
        hoveringIndex: number;
        inputHovering: boolean;
        selectionWidth: number;
        collapseItemWidth: number;
        previousQuery: string | null;
        selectedLabel: string;
        menuVisibleOnFocus: boolean;
        isBeforeHide: boolean;
    };
    isFocused: import("vue").Ref<boolean>;
    expanded: import("vue").Ref<boolean>;
    optionsArray: import("vue").ComputedRef<OptionPublicInstance[]>;
    hoverOption: import("vue").Ref<any>;
    selectSize: import("vue").ComputedRef<"" | "small" | "default" | "large">;
    filteredOptionsCount: import("vue").ComputedRef<number>;
    updateTooltip: () => void;
    updateTagTooltip: () => void;
    debouncedOnInputChange: import("lodash").DebouncedFunc<() => void>;
    onInput: (event: Event) => void;
    deletePrevTag: (e: KeyboardEvent) => void;
    deleteTag: (event: MouseEvent, tag: OptionPublicInstance | OptionBasic) => void;
    deleteSelected: (event: Event) => void;
    handleOptionSelect: (option: OptionPublicInstance) => void;
    scrollToOption: (option: OptionPublicInstance | OptionPublicInstance[] | SelectStates["selected"]) => void;
    hasModelValue: import("vue").ComputedRef<boolean>;
    shouldShowPlaceholder: import("vue").ComputedRef<boolean>;
    currentPlaceholder: import("vue").ComputedRef<string>;
    mouseEnterEventName: import("vue").ComputedRef<"mouseenter" | null>;
    needStatusIcon: import("vue").ComputedRef<boolean>;
    showClose: import("vue").ComputedRef<boolean>;
    iconComponent: import("vue").ComputedRef<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | Component) & {}) | (() => string | Component) | ((new (...args: any[]) => (string | Component) & {}) | (() => string | Component))[], unknown, unknown> | undefined>;
    iconReverse: import("vue").ComputedRef<string>;
    validateState: import("vue").ComputedRef<"" | "error" | "success" | "validating">;
    validateIcon: import("vue").ComputedRef<"" | Component>;
    showNewOption: import("vue").ComputedRef<boolean>;
    updateOptions: () => void;
    collapseTagSize: import("vue").ComputedRef<"default" | "small">;
    setSelected: () => void;
    selectDisabled: import("vue").ComputedRef<boolean>;
    emptyText: import("vue").ComputedRef<string | null>;
    handleCompositionStart: (event: CompositionEvent) => void;
    handleCompositionUpdate: (event: CompositionEvent) => void;
    handleCompositionEnd: (event: CompositionEvent) => void;
    onOptionCreate: (vm: OptionPublicInstance) => void;
    onOptionDestroy: (key: OptionValue, vm: OptionPublicInstance) => void;
    handleMenuEnter: () => void;
    focus: () => void;
    blur: () => void;
    handleClearClick: (event: Event) => void;
    handleClickOutside: (event: Event) => void;
    handleEsc: () => void;
    toggleMenu: () => void;
    selectOption: () => void;
    getValueKey: (item: OptionPublicInstance | SelectStates["selected"][0]) => any;
    navigateOptions: (direction: "prev" | "next") => void;
    dropdownMenuVisible: import("vue").WritableComputedRef<boolean>;
    showTagList: import("vue").ComputedRef<{
        value: OptionValue;
        currentLabel: OptionPublicInstance["currentLabel"];
        isDisabled?: OptionPublicInstance["isDisabled"] | undefined;
    }[]>;
    collapseTagList: import("vue").ComputedRef<{
        value: OptionValue;
        currentLabel: OptionPublicInstance["currentLabel"];
        isDisabled?: OptionPublicInstance["isDisabled"] | undefined;
    }[]>;
    popupScroll: (data: {
        scrollTop: number;
        scrollLeft: number;
    }) => void;
    tagStyle: import("vue").ComputedRef<{
        maxWidth: string;
    }>;
    collapseTagStyle: import("vue").ComputedRef<{
        maxWidth: string;
    }>;
    popperRef: import("vue").ComputedRef<HTMLElement | undefined>;
    inputRef: import("vue").Ref<HTMLInputElement | undefined>;
    tooltipRef: import("vue").Ref<TooltipInstance | undefined>;
    tagTooltipRef: import("vue").Ref<TooltipInstance | undefined>;
    prefixRef: import("vue").Ref<HTMLElement | undefined>;
    suffixRef: import("vue").Ref<HTMLElement | undefined>;
    selectRef: import("vue").Ref<HTMLElement | undefined>;
    wrapperRef: import("vue").ShallowRef<HTMLElement | undefined>;
    selectionRef: import("vue").Ref<HTMLElement | undefined>;
    scrollbarRef: import("vue").Ref<ScrollbarInstance | undefined>;
    menuRef: import("vue").Ref<HTMLElement | undefined>;
    tagMenuRef: import("vue").Ref<HTMLElement | undefined>;
    collapseItemRef: import("vue").Ref<HTMLElement | undefined>;
};
