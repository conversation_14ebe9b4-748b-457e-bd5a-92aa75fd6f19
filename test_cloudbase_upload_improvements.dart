import 'dart:convert';
import 'package:http/http.dart' as http;

/// 测试CloudBase直传功能的改进
class CloudBaseUploadImprovementTest {
  final String _baseUrl = 'https://tcb-api.tencentcloudapi.com/web';
  final String _envId = 'novel-app-2gywkgnn15cbd6a8';
  
  /// 测试多个API变体
  Future<void> testApiVariants() async {
    print('🧪 测试CloudBase API变体功能...');
    
    final apiVariants = [
      // 变体1：简化参数格式
      {
        'name': 'API变体 1',
        'body': {
          'action': 'storage.getUploadMetadata',
          'path': 'test-file.json',
        }
      },
      // 变体2：包含dataVersion
      {
        'name': 'API变体 2', 
        'body': {
          'action': 'storage.getUploadMetadata',
          'dataVersion': '2019-04-09',
          'path': 'test-file.json',
        }
      },
      // 变体3：使用cloudPath参数名
      {
        'name': 'API变体 3',
        'body': {
          'action': 'storage.getUploadMetadata',
          'cloudPath': 'test-file.json',
        }
      },
    ];

    // 逐个测试API变体
    for (int i = 0; i < apiVariants.length; i++) {
      final variant = apiVariants[i];
      print('\n🔄 测试${variant['name']}/${apiVariants.length}...');

      try {
        final response = await http.post(
          Uri.parse('$_baseUrl?env=$_envId'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test_token',
          },
          body: jsonEncode(variant['body']),
        );

        print('   📊 响应状态码: ${response.statusCode}');
        print('   📄 响应内容: ${response.body.substring(0, response.body.length > 200 ? 200 : response.body.length)}...');

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          if (data['code'] == 0) {
            print('   ✅ ${variant['name']}成功');
          } else {
            print('   ❌ ${variant['name']}失败: ${data['message']}');
          }
        } else {
          print('   ❌ ${variant['name']}请求失败');
        }
      } catch (e) {
        print('   ❌ ${variant['name']}异常: $e');
      }

      // 变体间延迟
      if (i < apiVariants.length - 1) {
        await Future.delayed(Duration(milliseconds: 500));
      }
    }
  }

  /// 测试数据统计功能
  void testDataStats() {
    print('\n🧪 测试数据统计功能...');
    
    final testData = {
      'novels': [
        {
          'title': '测试小说1',
          'content': '这是测试内容' * 100,
          'chapters': [
            {'title': '第一章', 'content': '章节内容1'},
            {'title': '第二章', 'content': '章节内容2'},
          ]
        },
        {
          'title': '测试小说2',
          'content': '另一个测试内容' * 50,
          'chapters': [
            {'title': '第一章', 'content': '章节内容1'},
          ]
        }
      ]
    };

    final stats = _generateDataStats(testData);
    print('📊 生成的数据统计:');
    print('   总大小: ${stats['totalSizeMB']} MB');
    print('   小说数量: ${stats['novelCount']}');
    print('   小说统计: ${stats['novelStats']}');
    print('   计算时间: ${stats['calculatedAt']}');
  }

  /// 生成数据统计信息（复制自CloudBase服务）
  Map<String, dynamic> _generateDataStats(Map<String, dynamic> data) {
    final stats = <String, dynamic>{};
    
    // 计算总数据大小
    final dataString = jsonEncode(data);
    final totalSizeBytes = dataString.length;
    final totalSizeMB = totalSizeBytes / (1024 * 1024);
    
    stats['totalSizeBytes'] = totalSizeBytes;
    stats['totalSizeMB'] = double.parse(totalSizeMB.toStringAsFixed(2));
    
    // 统计小说数据
    if (data.containsKey('novels')) {
      final novels = data['novels'] as List? ?? [];
      stats['novelCount'] = novels.length;
      
      // 统计前10本小说的详细信息
      final novelStats = <Map<String, dynamic>>[];
      for (int i = 0; i < novels.length && i < 10; i++) {
        final novel = novels[i];
        if (novel is Map<String, dynamic>) {
          final novelStat = <String, dynamic>{
            'title': novel['title'] ?? '未知标题',
          };
          
          // 统计内容长度
          if (novel.containsKey('content')) {
            novelStat['contentLength'] = (novel['content'] as String? ?? '').length;
          }
          
          // 统计章节数量
          if (novel.containsKey('chapters')) {
            final chapters = novel['chapters'] as List? ?? [];
            novelStat['chapterCount'] = chapters.length;
          }
          
          novelStats.add(novelStat);
        }
      }
      stats['novelStats'] = novelStats;
    }
    
    // 添加计算时间戳
    stats['calculatedAt'] = DateTime.now().toIso8601String();
    
    return stats;
  }

  /// 测试Mock服务器API
  Future<void> testMockServerApi() async {
    print('\n🧪 测试Mock服务器直传API...');
    
    try {
      final testData = {
        'novels': [{'title': '测试小说', 'content': '测试内容'}],
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await http.post(
        Uri.parse('https://novel-app-2gywkgnn15cbd6a8-1368800861.ap-shanghai.app.tcloudbase.com/api/sync/upload-direct'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test_token',
        },
        body: jsonEncode({
          'data': testData,
          'timestamp': DateTime.now().toIso8601String(),
          'fileId': 'test-file-id',
          'fileName': 'test-file.json',
          'dataStats': _generateDataStats(testData),
        }),
      );

      print('📊 Mock服务器响应状态码: ${response.statusCode}');
      print('📄 Mock服务器响应内容: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          print('✅ Mock服务器直传API测试成功');
        } else {
          print('❌ Mock服务器直传API测试失败: ${data['message']}');
        }
      } else {
        print('❌ Mock服务器直传API请求失败');
      }
    } catch (e) {
      print('❌ Mock服务器直传API测试异常: $e');
    }
  }

  /// 运行所有测试
  Future<void> runAllTests() async {
    print('🚀 开始CloudBase直传功能改进测试...\n');
    
    // 测试API变体
    await testApiVariants();
    
    // 测试数据统计
    testDataStats();
    
    // 测试Mock服务器API
    await testMockServerApi();
    
    print('\n🎉 所有测试完成！');
  }
}

/// 主函数
void main() async {
  final tester = CloudBaseUploadImprovementTest();
  await tester.runAllTests();
}
