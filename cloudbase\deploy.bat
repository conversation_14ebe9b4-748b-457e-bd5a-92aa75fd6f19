@echo off
setlocal enabledelayedexpansion

REM Tencent CloudBase Deployment Script (Windows)
REM Usage: deploy.bat [ENV_ID]

if "%1"=="" (
    echo [ERROR] Please provide ENV_ID as parameter
    echo Usage: deploy.bat [ENV_ID]
    exit /b 1
)

set ENV_ID=%1

echo [INFO] Starting deployment to CloudBase environment: %ENV_ID%

REM Check required tools
echo [INFO] Checking required tools...

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js not installed, please install Node.js first
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm not installed, please install npm first
    exit /b 1
)

where tcb >nul 2>nul
if %errorlevel% neq 0 (
    echo [INFO] CloudBase CLI not installed, installing...
    npm install -g @cloudbase/cli
)

REM Create deployment directory
set DEPLOY_DIR=cloudbase-deploy
echo [INFO] Creating deployment directory: %DEPLOY_DIR%

if exist "%DEPLOY_DIR%" (
    rmdir /s /q "%DEPLOY_DIR%"
)

mkdir "%DEPLOY_DIR%"

REM Copy necessary files
echo [INFO] Copying deployment files...
copy cloudbase-server.js "%DEPLOY_DIR%\"
copy index.js "%DEPLOY_DIR%\"
copy db.json "%DEPLOY_DIR%\"
copy cloudbase-package.json "%DEPLOY_DIR%\package.json"

REM Update config file
echo [INFO] Updating config file...
powershell -Command "(Get-Content cloudbaserc.json) -replace 'your-env-id', '%ENV_ID%' | Set-Content '%DEPLOY_DIR%\cloudbaserc.json'"

REM Enter deployment directory
cd "%DEPLOY_DIR%"

REM Install dependencies
echo [INFO] Installing project dependencies...
npm install

REM Check login status
echo [INFO] Checking CloudBase login status...
tcb auth list >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] Not logged in to CloudBase, please login first
    tcb login
)

REM Deploy cloud function
echo [INFO] Deploying cloud function...
tcb fn deploy novel-app-api --envId "%ENV_ID%"

REM Create HTTP service
echo [INFO] Creating HTTP service...
tcb service create -p /api -f novel-app-api --envId "%ENV_ID%" || echo [WARNING] HTTP service may already exist

REM Get access URL
echo [INFO] Deployment completed!
echo [INFO] API access URL: https://%ENV_ID%.service.tcloudbase.com/api

echo [INFO] Test API endpoints:
echo   Login: POST https://%ENV_ID%.service.tcloudbase.com/api/auth/login
echo   Register: POST https://%ENV_ID%.service.tcloudbase.com/api/auth/register
echo   Packages: GET https://%ENV_ID%.service.tcloudbase.com/api/packages

echo [INFO] Please update API config in Flutter app to above URL

REM Return to original directory
cd ..

echo [INFO] Deployment script completed!
pause
