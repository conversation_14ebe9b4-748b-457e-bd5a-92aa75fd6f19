import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false
    }
  },
  {
    path: '/',
    name: 'Layout',
    component: () => import('@/layout/index.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: '/dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: {
          title: '仪表板',
          icon: 'DataBoard'
        }
      },
      {
        path: '/users',
        name: 'Users',
        component: () => import('@/views/Users/<USER>'),
        meta: {
          title: '用户管理',
          icon: 'User'
        }
      },
      {
        path: '/users/:id',
        name: 'UserDetail',
        component: () => import('@/views/Users/<USER>'),
        meta: {
          title: '用户详情',
          hidden: true
        }
      },
      {
        path: '/novels',
        name: 'Novels',
        component: () => import('@/views/Novels/index.vue'),
        meta: {
          title: '小说管理',
          icon: 'Reading'
        }
      },
      {
        path: '/novels/:id',
        name: 'NovelDetail',
        component: () => import('@/views/Novels/Detail.vue'),
        meta: {
          title: '小说详情',
          hidden: true
        }
      },
      {
        path: '/members',
        name: 'Members',
        component: () => import('@/views/Members/index.vue'),
        meta: {
          title: '会员管理',
          icon: 'Postcard'
        }
      },
      {
        path: '/member-codes',
        name: 'MemberCodes',
        component: () => import('@/views/MemberCodes/index.vue'),
        meta: {
          title: '会员码管理',
          icon: 'Tickets'
        }
      },
      {
        path: '/sync',
        name: 'DataSync',
        component: () => import('@/views/DataSync/index.vue'),
        meta: {
          title: '数据同步',
          icon: 'Refresh'
        }
      },
      {
        path: '/analytics',
        name: 'Analytics',
        component: () => import('@/views/Analytics/index.vue'),
        meta: {
          title: '数据分析',
          icon: 'TrendCharts'
        }
      },
      {
        path: '/settings',
        name: 'Settings',
        component: () => import('@/views/Settings/index.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting'
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/404.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 小说应用后台管理系统`
  }

  // 检查是否需要登录
  if (to.meta?.requiresAuth !== false) {
    const token = localStorage.getItem('admin_token')
    if (!token) {
      next('/login')
      return
    }
  }

  next()
})

export default router
