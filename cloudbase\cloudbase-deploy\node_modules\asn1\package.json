{"author": "<PERSON><PERSON> (joyent.com)", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "name": "asn1", "description": "Contains parsers and serializers for ASN.1 (currently BER only)", "version": "0.2.6", "repository": {"type": "git", "url": "https://github.com/joyent/node-asn1.git"}, "main": "lib/index.js", "dependencies": {"safer-buffer": "~2.1.0"}, "devDependencies": {"istanbul": "^0.3.6", "faucet": "0.0.1", "tape": "^3.5.0", "eslint": "2.13.1", "eslint-plugin-joyent": "~1.3.0"}, "scripts": {"test": "./node_modules/.bin/tape ./test/ber/*.test.js"}, "license": "MIT"}