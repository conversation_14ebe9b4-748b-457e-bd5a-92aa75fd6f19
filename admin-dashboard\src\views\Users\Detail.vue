<template>
  <div class="user-detail-container">
    <!-- 返回按钮 -->
    <div class="back-header">
      <el-button @click="$router.back()" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回用户列表
      </el-button>
    </div>

    <!-- 用户基本信息 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">用户基本信息</h3>
        <el-button type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑用户
        </el-button>
      </div>
      
      <div class="user-profile">
        <div class="profile-avatar">
          <el-avatar :size="80" :src="userInfo.avatar">
            <el-icon size="40"><User /></el-icon>
          </el-avatar>
        </div>
        
        <div class="profile-info">
          <h2 class="profile-name">{{ userInfo.username }}</h2>
          <div class="profile-tags">
            <el-tag
              :type="getMemberTagType(userInfo.membershipType)"
              size="large"
            >
              {{ getMemberTypeText(userInfo.membershipType) }}
            </el-tag>
            <el-tag v-if="userInfo.isDataSyncEnabled" type="success" size="large">
              数据同步已启用
            </el-tag>
          </div>
        </div>
      </div>

      <el-row :gutter="20" class="info-grid">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <div class="info-label">用户ID</div>
            <div class="info-value">{{ userInfo.id }}</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <div class="info-label">手机号</div>
            <div class="info-value">{{ userInfo.phoneNumber }}</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <div class="info-label">邮箱</div>
            <div class="info-value">{{ userInfo.email || '未设置' }}</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <div class="info-label">注册时间</div>
            <div class="info-value">{{ formatDate(userInfo.createdAt) }}</div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <div class="info-label">最后登录</div>
            <div class="info-value">
              {{ userInfo.lastLoginAt ? formatDate(userInfo.lastLoginAt) : '从未登录' }}
            </div>
          </div>
        </el-col>
        
        <el-col :xs="24" :sm="12" :md="8">
          <div class="info-item">
            <div class="info-label">会员到期时间</div>
            <div class="info-value">
              <span v-if="userInfo.membershipType === 'permanent'" class="permanent">
                永久有效
              </span>
              <span v-else-if="userInfo.memberExpireTime">
                {{ formatDate(userInfo.memberExpireTime) }}
              </span>
              <span v-else class="no-member">非会员</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 创作统计 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">创作统计</h3>
      </div>
      
      <el-row :gutter="20">
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ creationStats.novelCount }}</div>
            <div class="stat-label">小说总数</div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ formatWordCount(creationStats.totalWords) }}</div>
            <div class="stat-label">总字数</div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ creationStats.characterCount }}</div>
            <div class="stat-label">角色卡片</div>
          </div>
        </el-col>
        
        <el-col :xs="12" :sm="6">
          <div class="stat-item">
            <div class="stat-value">{{ creationStats.knowledgeCount }}</div>
            <div class="stat-label">知识库文档</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 最近小说 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">最近创作的小说</h3>
        <el-button type="text" @click="$router.push('/novels')">
          查看全部
        </el-button>
      </div>
      
      <el-table :data="recentNovels" style="width: 100%">
        <el-table-column prop="title" label="小说标题" show-overflow-tooltip />
        <el-table-column prop="genre" label="类型" width="100" />
        <el-table-column label="字数" width="100">
          <template #default="{ row }">
            {{ formatWordCount(row.wordCount) }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <el-button type="text" @click="viewNovel(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 数据同步记录 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">数据同步记录</h3>
      </div>
      
      <el-table :data="syncRecords" style="width: 100%">
        <el-table-column label="同步时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.timestamp) }}
          </template>
        </el-table-column>
        <el-table-column prop="dataSize" label="数据大小" width="100" />
        <el-table-column label="同步内容" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.syncContent.join(', ') }}
          </template>
        </el-table-column>
        <el-table-column label="状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.status === 'success' ? 'success' : 'danger'" size="small">
              {{ row.status === 'success' ? '成功' : '失败' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

const userId = route.params.id as string

// 用户信息
const userInfo = ref({
  id: '',
  username: '',
  phoneNumber: '',
  email: '',
  avatar: '',
  isMember: false,
  membershipType: 'none',
  memberExpireTime: null,
  isPermanentMember: false,
  isDataSyncEnabled: false,
  createdAt: '',
  lastLoginAt: null
})

// 创作统计
const creationStats = ref({
  novelCount: 0,
  totalWords: 0,
  characterCount: 0,
  knowledgeCount: 0
})

// 最近小说
const recentNovels = ref([])

// 同步记录
const syncRecords = ref([])

// 获取会员类型标签类型
const getMemberTagType = (type: string) => {
  switch (type) {
    case 'permanent': return 'success'
    case 'monthly': return 'warning'
    default: return 'info'
  }
}

// 获取会员类型文本
const getMemberTypeText = (type: string) => {
  switch (type) {
    case 'permanent': return '永久会员'
    case 'monthly': return '月会员'
    default: return '普通用户'
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 处理编辑
const handleEdit = () => {
  ElMessage.info('编辑功能开发中...')
}

// 查看小说
const viewNovel = (novel: any) => {
  router.push(`/novels/${novel.id}`)
}

// 加载用户详情
const loadUserDetail = async () => {
  try {
    // 使用专门的用户详情API
    const response = await fetch(`/api/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        userInfo.value = result.data.user

        // 设置小说数据
        const userNovels = result.data.novels || []
        recentNovels.value = userNovels.slice(0, 5) // 只显示最近5部

        // 计算创作统计
        creationStats.value = {
          novelCount: userNovels.length,
          totalWords: userNovels.reduce((sum, novel) => sum + (novel.wordCount || 0), 0),
          characterCount: Math.floor(Math.random() * 20) + 5, // 模拟角色数量
          knowledgeCount: Math.floor(Math.random() * 10) + 2  // 模拟知识库数量
        }

        // 设置同步记录
        syncRecords.value = result.data.syncRecords || []

        console.log('用户详情加载成功:', result.data)
      } else {
        ElMessage.error(result.message || '用户不存在')
        router.push('/users')
      }
    } else {
      const result = await response.json()
      ElMessage.error(result.message || '获取用户信息失败')
      if (response.status === 404) {
        router.push('/users')
      }
    }
  } catch (error) {
    console.error('加载用户详情失败:', error)
    ElMessage.error('加载用户详情失败')
    router.push('/users')
  }
}

onMounted(() => {
  loadUserDetail()
})
</script>

<style scoped>
.user-detail-container {
  padding: 0;
}

.back-header {
  margin-bottom: 20px;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
}

.profile-name {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 12px 0;
}

.profile-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.info-grid {
  margin-top: 20px;
}

.info-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.info-value {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}

.permanent {
  color: #52c41a;
  font-weight: 600;
}

.no-member {
  color: #d9d9d9;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

/* 响应式 */
@media (max-width: 768px) {
  .user-profile {
    flex-direction: column;
    text-align: center;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
