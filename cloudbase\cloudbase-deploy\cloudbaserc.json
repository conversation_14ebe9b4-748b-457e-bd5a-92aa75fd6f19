{"envId": "novel-app-2gywkgnn15cbd6a8", "functionRoot": "./", "functions": [{"name": "novel-app-api", "timeout": 60, "envVariables": {}, "runtime": "Nodejs12.16", "memorySize": 256, "handler": "index.main"}], "framework": {"name": "node-app", "plugins": {"function": {"use": "@cloudbase/framework-plugin-function", "inputs": {"functionRootPath": "./", "functions": [{"name": "novel-app-api", "timeout": 60, "envVariables": {}, "runtime": "Nodejs12.16", "memorySize": 256, "handler": "index.main"}]}}, "api": {"use": "@cloudbase/framework-plugin-api", "inputs": {"name": "novel-app-api", "path": "/api", "protocols": ["http", "https"]}}}}}